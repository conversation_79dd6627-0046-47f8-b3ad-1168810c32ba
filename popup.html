<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Saver</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <span class="icon">📚</span>
                Page Saver
            </h1>
        </header>

        <div class="current-page">
            <div class="page-info">
                <div class="page-type" id="pageType">
                    <span class="type-icon" id="typeIcon">📄</span>
                    <span class="type-text" id="typeText">Loading...</span>
                </div>
                <div class="page-title" id="pageTitle">Loading page information...</div>
                <div class="page-url" id="pageUrl"></div>
            </div>
            <button class="save-btn" id="saveBtn" disabled>
                <span class="btn-icon">💾</span>
                <span class="btn-text">Save Page</span>
            </button>
        </div>

        <div class="search-section">
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Search saved pages..." class="search-input">
                <button class="search-btn" id="searchBtn">🔍</button>
            </div>
            <div class="filters-container" id="filtersContainer">
                <select id="categoryFilter" class="filter-select">
                    <option value="">All Categories</option>
                </select>
                <select id="typeFilter" class="filter-select">
                    <option value="">All Types</option>
                </select>
                <select id="sortFilter" class="filter-select">
                    <option value="timestamp">Newest First</option>
                    <option value="title">Title A-Z</option>
                    <option value="readingTime">Reading Time</option>
                </select>
            </div>
        </div>

        <div class="saved-section">
            <div class="section-header">
                <h2>Saved Pages</h2>
                <div class="actions">
                    <span class="count" id="savedCount">0 items</span>
                    <button class="podcast-btn" id="podcastBtn" title="Generate AI Podcast">🎙️</button>
                    <button class="stats-btn" id="statsBtn" title="View statistics">📊</button>
                    <button class="clear-btn" id="clearBtn" title="Clear all">🗑️</button>
                </div>
            </div>

            <div class="saved-list" id="savedList">
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">📭</div>
                    <div class="empty-text">No saved pages yet</div>
                    <div class="empty-subtext">Click "Save Page" to get started</div>
                </div>
            </div>
        </div>

        <div class="preview-modal" id="previewModal" style="display: none;">
            <div class="preview-content">
                <div class="preview-header">
                    <h3 id="previewTitle">Content Preview</h3>
                    <button class="close-btn" id="closePreview">✕</button>
                </div>
                <div class="preview-body" id="previewBody">
                    <div class="preview-meta" id="previewMeta"></div>
                    <div class="preview-text" id="previewText"></div>
                </div>
            </div>
        </div>

        <div class="stats-modal" id="statsModal" style="display: none;">
            <div class="stats-content">
                <div class="stats-header">
                    <h3>Statistics</h3>
                    <button class="close-btn" id="closeStats">✕</button>
                </div>
                <div class="stats-body" id="statsBody">
                    <!-- Stats content will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <div class="podcast-modal" id="podcastModal" style="display: none;">
            <div class="podcast-content">
                <div class="podcast-header">
                    <h3>🎙️ Generate AI Podcast</h3>
                    <button class="close-btn" id="closePodcast">✕</button>
                </div>
                <div class="podcast-body" id="podcastBody">
                    <div class="podcast-settings">
                        <div class="setting-group">
                            <label>Host Personalities</label>
                            <div class="host-selection">
                                <select id="hostA" class="host-select">
                                    <option value="alex">Alex (Analytical & Thoughtful)</option>
                                    <option value="sam" selected>Sam (Enthusiastic & Energetic)</option>
                                    <option value="jordan">Jordan (Practical & Grounded)</option>
                                    <option value="riley">Riley (Creative & Imaginative)</option>
                                </select>
                                <span class="host-separator">+</span>
                                <select id="hostB" class="host-select">
                                    <option value="alex" selected>Alex (Analytical & Thoughtful)</option>
                                    <option value="sam">Sam (Enthusiastic & Energetic)</option>
                                    <option value="jordan">Jordan (Practical & Grounded)</option>
                                    <option value="riley">Riley (Creative & Imaginative)</option>
                                </select>
                            </div>
                        </div>

                        <div class="setting-group">
                            <label>Episode Length</label>
                            <select id="podcastLength" class="setting-select">
                                <option value="5">5 minutes (Quick overview)</option>
                                <option value="10" selected>10 minutes (Standard)</option>
                                <option value="15">15 minutes (Deep dive)</option>
                                <option value="20">20 minutes (Comprehensive)</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label>Focus Area</label>
                            <select id="podcastFocus" class="setting-select">
                                <option value="general" selected>All Content</option>
                                <option value="technology">Technology</option>
                                <option value="business">Business</option>
                                <option value="science">Science</option>
                                <option value="entertainment">Entertainment</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label>Conversation Style</label>
                            <select id="podcastStyle" class="setting-select">
                                <option value="conversational" selected>Natural & Conversational</option>
                                <option value="analytical">Analytical & Deep</option>
                                <option value="enthusiastic">Energetic & Enthusiastic</option>
                                <option value="professional">Professional & Structured</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label>Advanced Options</label>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="includeMusic" checked>
                                    Include background music
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="includeReflections" checked>
                                    Include AI personal reflections
                                </label>
                            </div>
                        </div>

                        <div class="api-keys-section" id="apiKeysSection">
                            <div class="setting-group">
                                <label>API Keys (Optional for Demo)</label>
                                <input type="password" id="openaiKey" placeholder="OpenAI API Key (leave empty for demo)" class="api-input">
                                <input type="password" id="elevenlabsKey" placeholder="ElevenLabs API Key (leave empty for demo)" class="api-input">
                                <div class="api-help">
                                    <small>
                                        <strong>Demo Mode:</strong> Leave both fields empty to generate a sample podcast using your saved content.<br>
                                        <strong>Full Mode:</strong> Add API keys for GPT-4 script generation and professional voice synthesis.<br>
                                        Get API keys:
                                        <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI</a> |
                                        <a href="https://elevenlabs.io/app/speech-synthesis" target="_blank">ElevenLabs</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="podcast-actions">
                        <button class="generate-btn" id="generatePodcast">
                            <span class="btn-icon">🎙️</span>
                            <span class="btn-text">Generate Podcast</span>
                        </button>
                    </div>

                    <div class="podcast-progress" id="podcastProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text" id="progressText">Preparing...</div>
                    </div>

                    <div class="podcast-result" id="podcastResult" style="display: none;">
                        <div class="result-header">
                            <h4>🎉 Podcast Generated Successfully!</h4>
                        </div>
                        <div class="audio-player">
                            <audio controls id="podcastAudio" style="width: 100%;">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                        <div class="result-actions">
                            <button class="download-btn" id="downloadPodcast">
                                <span class="btn-icon">⬇️</span>
                                Download MP3
                            </button>
                            <button class="script-btn" id="viewScript">
                                <span class="btn-icon">📄</span>
                                View Script
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="status-message" id="statusMessage"></div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
