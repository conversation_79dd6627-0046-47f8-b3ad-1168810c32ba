<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Saver</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <span class="icon">📚</span>
                Page Saver
            </h1>
        </header>

        <div class="current-page">
            <div class="page-info">
                <div class="page-type" id="pageType">
                    <span class="type-icon" id="typeIcon">📄</span>
                    <span class="type-text" id="typeText">Loading...</span>
                </div>
                <div class="page-title" id="pageTitle">Loading page information...</div>
                <div class="page-url" id="pageUrl"></div>
            </div>
            <button class="save-btn" id="saveBtn" disabled>
                <span class="btn-icon">💾</span>
                <span class="btn-text">Save Page</span>
            </button>
        </div>

        <div class="saved-section">
            <div class="section-header">
                <h2>Saved Pages</h2>
                <div class="actions">
                    <span class="count" id="savedCount">0 items</span>
                    <button class="clear-btn" id="clearBtn" title="Clear all">🗑️</button>
                </div>
            </div>
            
            <div class="saved-list" id="savedList">
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">📭</div>
                    <div class="empty-text">No saved pages yet</div>
                    <div class="empty-subtext">Click "Save Page" to get started</div>
                </div>
            </div>
        </div>

        <div class="status-message" id="statusMessage"></div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
