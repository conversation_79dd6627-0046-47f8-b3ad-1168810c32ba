// Complete Podcast Generation System
class PodcastGenerator {
    constructor() {
        this.scriptGenerator = new ScriptGenerator();
        this.voiceSynthesizer = new VoiceSynthesizer();
        this.openAIApiKey = null;
        this.elevenLabsApiKey = null;
        
        this.templates = {
            opening: {
                casual: "Hey everyone, welcome back! We've got some really interesting content to dive into today.",
                professional: "Welcome to our content discussion. Today we're exploring several fascinating pieces that caught our attention.",
                enthusiastic: "Oh wow, do we have some incredible content for you today! We're practically buzzing with excitement to share these insights."
            },
            transitions: {
                natural: ["Speaking of which", "That reminds me", "Building on that idea", "What's fascinating is"],
                analytical: ["Let's examine", "Consider this", "The data suggests", "Looking deeper"],
                conversational: ["You know what", "Here's the thing", "I was just thinking", "Actually"]
            },
            closings: {
                reflective: "As we wrap up, it's clear that these insights point to some really important trends.",
                forward_looking: "Looking ahead, these developments suggest we're in for some exciting changes.",
                summary: "To summarize the key takeaways from today's discussion..."
            }
        };

        this.musicTracks = {
            intro: {
                url: 'assets/music/intro-ambient.mp3',
                duration: 10000, // 10 seconds
                fadeIn: 2000,
                fadeOut: 3000
            },
            background: {
                url: 'assets/music/background-subtle.mp3',
                volume: 0.15,
                loop: true
            },
            outro: {
                url: 'assets/music/outro-uplifting.mp3',
                duration: 8000,
                fadeIn: 2000,
                fadeOut: 2000
            }
        };
    }

    setApiKeys(openAIKey, elevenLabsKey) {
        this.openAIApiKey = openAIKey;
        this.elevenLabsApiKey = elevenLabsKey;
        this.voiceSynthesizer.setApiKey(elevenLabsKey);
    }

    async generatePodcast(savedItems, options = {}) {
        const {
            hostA = 'alex',
            hostB = 'sam',
            targetLength = 10, // minutes
            focusArea = 'general',
            style = 'conversational',
            includeMusic = true,
            includePersonalReflections = true,
            customPrompt = null,
            voiceSpeed = 1.0,
            musicVolume = 0.3
        } = options;

        try {
            // Validate inputs
            this.validateInputs(savedItems, options);

            // Generate the conversational script
            console.log('Generating conversational script...');
            const scriptResult = await this.generateEnhancedScript(
                savedItems, 
                hostA, 
                hostB, 
                targetLength, 
                focusArea, 
                style, 
                includePersonalReflections,
                customPrompt
            );

            // Synthesize voices
            console.log('Synthesizing voices...');
            const audioResult = await this.voiceSynthesizer.synthesizeScript(
                scriptResult.script,
                hostA,
                hostB,
                {
                    includeBackgroundMusic: includeMusic,
                    musicVolume: musicVolume,
                    voiceSpeed: voiceSpeed
                }
            );

            // Add background music and effects
            if (includeMusic) {
                console.log('Adding background music...');
                audioResult.audioBlob = await this.addBackgroundMusic(
                    audioResult.audioBlob,
                    musicVolume
                );
            }

            return {
                audioBlob: audioResult.audioBlob,
                script: scriptResult.script,
                metadata: {
                    ...scriptResult.metadata,
                    ...audioResult.metadata,
                    options: options,
                    generatedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('Error generating podcast:', error);
            throw new Error(`Podcast generation failed: ${error.message}`);
        }
    }

    validateInputs(savedItems, options) {
        if (!savedItems || savedItems.length === 0) {
            throw new Error('No saved items provided for podcast generation');
        }

        if (!this.openAIApiKey) {
            throw new Error('OpenAI API key not configured');
        }

        if (!this.elevenLabsApiKey) {
            throw new Error('ElevenLabs API key not configured');
        }

        if (options.targetLength && (options.targetLength < 2 || options.targetLength > 30)) {
            throw new Error('Target length must be between 2 and 30 minutes');
        }
    }

    async generateEnhancedScript(savedItems, hostA, hostB, targetLength, focusArea, style, includePersonalReflections, customPrompt) {
        // Use custom prompt if provided, otherwise generate with GPT-4
        if (customPrompt) {
            return await this.generateScriptWithCustomPrompt(customPrompt, savedItems, hostA, hostB);
        }

        // Prepare content summary
        const contentSummary = this.scriptGenerator.prepareContentSummary(savedItems, focusArea);
        
        // Generate script with GPT-4
        const prompt = this.buildAdvancedPrompt(contentSummary, hostA, hostB, targetLength, style, includePersonalReflections);
        
        const script = await this.callOpenAI(prompt);
        
        return {
            script: script,
            metadata: {
                hostA: this.scriptGenerator.hostPersonalities[hostA],
                hostB: this.scriptGenerator.hostPersonalities[hostB],
                estimatedLength: targetLength,
                contentItems: savedItems.length,
                focusArea: focusArea,
                style: style,
                generatedAt: new Date().toISOString()
            }
        };
    }

    buildAdvancedPrompt(contentSummary, hostA, hostB, targetLength, style, includePersonalReflections) {
        const hostAProfile = this.scriptGenerator.hostPersonalities[hostA];
        const hostBProfile = this.scriptGenerator.hostPersonalities[hostB];
        
        return `Create a natural, engaging podcast conversation between two AI hosts discussing saved web content. This should sound like NotebookLM quality - authentic, insightful, and genuinely conversational.

HOST PERSONALITIES:
${hostAProfile.name}: ${hostAProfile.traits.join(', ')} - ${hostAProfile.voice}
- Speaking style: ${hostAProfile.tendencies.join(', ')}
- Common expressions: ${hostAProfile.catchphrases.join(', ')}
- Interruption style: ${hostAProfile.interruption_style}

${hostBProfile.name}: ${hostBProfile.traits.join(', ')} - ${hostBProfile.voice}
- Speaking style: ${hostBProfile.tendencies.join(', ')}
- Common expressions: ${hostBProfile.catchphrases.join(', ')}
- Interruption style: ${hostBProfile.interruption_style}

CONTENT TO DISCUSS:
${contentSummary.items.map((item, index) => `
${index + 1}. "${item.title}" (${item.type})
   - Category: ${item.category}
   - Source: ${item.siteName || 'Unknown'}
   - Reading time: ${item.readingTime || 'Unknown'} minutes
   - Key insights: ${item.keyPoints.join('; ')}
   - Tags: ${item.tags.join(', ')}
`).join('')}

IDENTIFIED THEMES: ${contentSummary.themes.map(t => `${t.theme} (${t.count} items)`).join(', ')}
TOTAL READING TIME: ${contentSummary.totalReadingTime} minutes

CONVERSATION REQUIREMENTS:
- Target duration: ${targetLength} minutes (approximately ${Math.floor(targetLength * 150)} words)
- Style: ${style} - natural, authentic, engaging
- Include realistic interruptions, overlaps, and natural speech patterns
- Show genuine curiosity, surprise, and thoughtful reactions
- Reference specific details, quotes, and insights from the content
- Build on each other's ideas organically
- Include natural "thinking out loud" moments
- Add appropriate pauses and emphasis
${includePersonalReflections ? '- Include AI-generated personal reflections (clearly marked as such)' : ''}

CONVERSATION STRUCTURE:
1. Engaging opening (45-60 seconds)
   - Natural greeting and rapport
   - Preview of exciting content ahead
   - Set conversational tone

2. Main discussion (${targetLength - 2} minutes)
   - Explore each piece of content naturally
   - Make connections between different articles
   - Share insights and reactions
   - Build momentum through the conversation

3. Thoughtful closing (45-60 seconds)
   - Synthesize key insights
   - Reflect on broader implications
   - Forward-looking statements

DIALOGUE FORMATTING:
- [${hostAProfile.name.toUpperCase()}]: dialogue text
- [${hostBProfile.name.toUpperCase()}]: dialogue text
- [PAUSE] for natural breaks (0.5-1 second)
- [THINKING] for contemplative moments
- [OVERLAPPING] for interruptions
- [EMPHASIS] for stressed words/phrases
- [EXCITED] [THOUGHTFUL] [SURPRISED] for emotional context

QUALITY STANDARDS:
- Sound like real people having a genuine conversation
- Reference specific details from the content naturally
- Show authentic reactions and build on ideas
- Include natural speech patterns and hesitations
- Make the content accessible and engaging
- Maintain energy and interest throughout

Generate a complete, natural conversation that feels authentic and informative, matching the quality of NotebookLM's AI-generated discussions.`;
    }

    async callOpenAI(prompt) {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.openAIApiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-4-turbo-preview',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert podcast script writer specializing in creating natural, engaging conversations between AI hosts. Your scripts should sound authentic, insightful, and genuinely conversational, matching the quality of NotebookLM.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 4000,
                temperature: 0.8,
                top_p: 0.9
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    async generateScriptWithCustomPrompt(customPrompt, savedItems, hostA, hostB) {
        const contentContext = savedItems.map(item => ({
            title: item.title,
            description: item.description,
            category: item.category?.primary,
            url: item.url
        }));

        const enhancedPrompt = `${customPrompt}

CONTENT CONTEXT:
${JSON.stringify(contentContext, null, 2)}

HOST NAMES: ${hostA} and ${hostB}

Generate a natural conversation script using the format:
[${hostA.toUpperCase()}]: dialogue
[${hostB.toUpperCase()}]: dialogue`;

        const script = await this.callOpenAI(enhancedPrompt);
        
        return {
            script: script,
            metadata: {
                customPrompt: true,
                hostA: hostA,
                hostB: hostB,
                contentItems: savedItems.length
            }
        };
    }

    async addBackgroundMusic(audioBlob, musicVolume) {
        // This would integrate with Web Audio API for sophisticated audio mixing
        // For now, return the original audio
        // In production, this would:
        // 1. Load background music tracks
        // 2. Mix them with the voice audio at appropriate levels
        // 3. Add intro/outro music
        // 4. Apply audio effects and mastering
        
        console.log('Background music integration would happen here');
        return audioBlob;
    }

    async exportPodcast(audioBlob, format = 'mp3', quality = '320kbps') {
        // Convert and export in desired format
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `podcast-${timestamp}.${format}`;
        
        // Create download link
        const url = URL.createObjectURL(audioBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        
        // Cleanup
        setTimeout(() => URL.revokeObjectURL(url), 1000);
        
        return filename;
    }

    async getGenerationProgress() {
        // Return current generation progress for UI updates
        return {
            stage: this.currentStage || 'idle',
            progress: this.currentProgress || 0,
            message: this.currentMessage || 'Ready to generate'
        };
    }

    setProgress(stage, progress, message) {
        this.currentStage = stage;
        this.currentProgress = progress;
        this.currentMessage = message;
        
        // Emit progress event for UI updates
        window.dispatchEvent(new CustomEvent('podcastProgress', {
            detail: { stage, progress, message }
        }));
    }
}

// Make PodcastGenerator available globally
window.PodcastGenerator = PodcastGenerator;
