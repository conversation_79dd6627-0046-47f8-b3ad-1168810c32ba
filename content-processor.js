// Advanced content processing module
class ContentProcessor {
    constructor() {
        this.readabilityConfig = {
            minContentLength: 140,
            minScore: 20,
            retryLength: 250,
            minTextLength: 25,
            unlikelyCandidates: /banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|foot|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,
            okMaybeItsACandidate: /and|article|body|column|main|shadow/i,
            positive: /article|body|content|entry|hentry|h-entry|main|page|pagination|post|text|blog|story/i,
            negative: /hidden|^hid$| hid$| hid |^hid |banner|combx|comment|com-|contact|foot|footer|footnote|masthead|media|meta|outbrain|promo|related|scroll|share|shoutbox|sidebar|skyscraper|sponsor|shopping|tags|tool|widget/i,
            divToPElements: /<(a|blockquote|dl|div|img|ol|p|pre|table|ul)/i,
            replaceBrs: /(<br[^>]*>[ \n\r\t]*){2,}/gi,
            replaceFonts: /<(\/?)font[^>]*>/gi,
            trim: /^\s+|\s+$/g,
            normalize: /\s{2,}/g,
            killBreaks: /(<br\s*\/?>(\s|&nbsp;?)*){1,}/g,
            videos: /\/\/(www\.)?(dailymotion|youtube|youtube-nocookie|player\.vimeo)\.com/i,
            skipFootnoteLink: /^\s*(\[?[a-z0-9]{1,2}\]?|^|edit|citation needed)\s*$/i,
            nextLink: /(next|weiter|continue|>([^\|]|$)|»([^\|]|$))/i,
            prevLink: /(prev|earl|old|new|<|«)/i
        };
    }

    async processContent(pageData, tabId) {
        try {
            const enhancedData = { ...pageData };
            
            // Extract full content based on type
            switch (pageData.type.type) {
                case 'article':
                    enhancedData.content = await this.extractArticleContent(tabId);
                    enhancedData.readingTime = this.estimateReadingTime(enhancedData.content.text);
                    break;
                case 'video':
                    if (pageData.type.subtype === 'youtube') {
                        enhancedData.content = await this.extractYouTubeContent(pageData, tabId);
                    }
                    break;
                case 'document':
                    if (pageData.type.subtype === 'pdf') {
                        enhancedData.content = await this.extractPDFContent(tabId);
                    }
                    break;
                default:
                    enhancedData.content = await this.extractGenericContent(tabId);
                    enhancedData.readingTime = this.estimateReadingTime(enhancedData.content?.text || '');
            }

            // Add categorization and tags
            enhancedData.category = await this.categorizeContent(enhancedData);
            enhancedData.tags = await this.extractTags(enhancedData);
            enhancedData.topics = await this.extractTopics(enhancedData);
            
            // Enhanced metadata
            enhancedData.favicon = await this.extractFavicon(tabId);
            enhancedData.siteName = await this.extractSiteName(tabId);
            enhancedData.wordCount = this.countWords(enhancedData.content?.text || enhancedData.description || '');
            
            // Processing metadata
            enhancedData.processed = {
                timestamp: Date.now(),
                version: '2.0',
                processingTime: Date.now() - pageData.timestamp
            };

            return enhancedData;
        } catch (error) {
            console.error('Error processing content:', error);
            return { ...pageData, processingError: error.message };
        }
    }

    async extractArticleContent(tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId },
                func: this.readabilityExtractor,
                args: [this.readabilityConfig]
            });

            if (results && results[0] && results[0].result) {
                return results[0].result;
            }
            return { text: '', html: '', excerpt: '' };
        } catch (error) {
            console.error('Error extracting article content:', error);
            return { text: '', html: '', excerpt: '' };
        }
    }

    readabilityExtractor(config) {
        // Simplified readability algorithm implementation
        function getInnerText(e) {
            return e.textContent || e.innerText || '';
        }

        function getCharCount(e, s) {
            s = s || ',';
            return getInnerText(e).split(s).length - 1;
        }

        function cleanStyles(e) {
            e = e || document;
            const cur = e.querySelectorAll('style');
            for (let i = 0; i < cur.length; i++) {
                cur[i].textContent = '';
            }
            return e;
        }

        function killBreaks(e) {
            e.innerHTML = e.innerHTML.replace(config.killBreaks, '<br />');
        }

        function clean(e, tag) {
            const targetList = e.getElementsByTagName(tag);
            const isEmbed = (tag === 'object' || tag === 'embed' || tag === 'iframe');

            for (let y = targetList.length - 1; y >= 0; y--) {
                const target = targetList[y];
                const attributeValues = '';
                for (let i = 0, il = target.attributes.length; i < il; i++) {
                    attributeValues += target.attributes[i].value + '|';
                }

                if ((attributeValues.search(config.videos) !== -1 && isEmbed) ||
                    attributeValues.search(config.videos) === -1 && isEmbed) {
                    if (target.parentNode) {
                        target.parentNode.removeChild(target);
                    }
                }
            }
        }

        function getArticleTitle() {
            let curTitle = '';
            let origTitle = '';

            try {
                curTitle = origTitle = document.title;
                
                if (typeof curTitle !== 'string') {
                    curTitle = origTitle = getInnerText(document.getElementsByTagName('title')[0]);
                }
            } catch (e) {}

            if (curTitle.match(/ [\|\-] /)) {
                curTitle = origTitle.replace(/(.*)[\|\-] .*/gi, '$1');
                
                if (curTitle.split(' ').length < 3) {
                    curTitle = origTitle.replace(/[^\|\-]*[\|\-](.*)/gi, '$1');
                }
            } else if (curTitle.indexOf(': ') !== -1) {
                curTitle = origTitle.replace(/.*:(.*)/gi, '$1');

                if (curTitle.split(' ').length < 3) {
                    curTitle = origTitle.replace(/[^:]*[:](.*)/gi, '$1');
                }
            } else if (curTitle.length > 150 || curTitle.length < 15) {
                const hOnes = document.getElementsByTagName('h1');
                if (hOnes.length === 1) {
                    curTitle = getInnerText(hOnes[0]);
                }
            }

            curTitle = curTitle.replace(config.trim, '');

            if (curTitle.split(' ').length <= 4) {
                curTitle = origTitle;
            }

            return curTitle;
        }

        function grabArticle() {
            const stripUnlikelyCandidates = true;
            const isPaging = false;
            let allElements = document.getElementsByTagName('*');
            let node = null;
            let nodesToScore = [];
            let candidates = [];

            for (let nodeIndex = 0; nodeIndex < allElements.length; nodeIndex++) {
                node = allElements[nodeIndex];

                const unlikelyMatchString = node.className + node.id;
                if (stripUnlikelyCandidates &&
                    unlikelyMatchString.search(config.unlikelyCandidates) !== -1 &&
                    unlikelyMatchString.search(config.okMaybeItsACandidate) === -1 &&
                    node.tagName !== 'BODY') {
                    continue;
                }

                if (node.tagName === 'P' || node.tagName === 'TD' || node.tagName === 'PRE') {
                    nodesToScore[nodesToScore.length] = node;
                }

                if (node.tagName === 'DIV') {
                    if (node.innerHTML.search(config.divToPElements) === -1) {
                        const newNode = document.createElement('p');
                        try {
                            newNode.innerHTML = node.innerHTML;
                            node.parentNode.replaceChild(newNode, node);
                            nodeIndex--;
                            nodesToScore[nodesToScore.length] = node;
                        } catch (e) {}
                    }
                }
            }

            for (let pt = 0; pt < nodesToScore.length; pt++) {
                const parentNode = nodesToScore[pt].parentNode;
                const grandParentNode = parentNode ? parentNode.parentNode : null;
                const innerText = getInnerText(nodesToScore[pt]);

                if (!parentNode || typeof(parentNode.tagName) === 'undefined') {
                    continue;
                }

                if (innerText.length < config.minTextLength) {
                    continue;
                }

                let contentScore = 1;
                contentScore += innerText.split(',').length;
                contentScore += Math.min(Math.floor(innerText.length / 100), 3);

                if (!parentNode.readability) {
                    parentNode.readability = { contentScore: 0 };
                    candidates.push(parentNode);
                }

                if (!grandParentNode.readability) {
                    grandParentNode.readability = { contentScore: 0 };
                    candidates.push(grandParentNode);
                }

                parentNode.readability.contentScore += contentScore;
                grandParentNode.readability.contentScore += contentScore / 2;
            }

            let topCandidate = null;
            for (let c = 0, cl = candidates.length; c < cl; c++) {
                candidates[c].readability.contentScore = candidates[c].readability.contentScore * this.getClassWeight(candidates[c]);

                if (!topCandidate || candidates[c].readability.contentScore > topCandidate.readability.contentScore) {
                    topCandidate = candidates[c];
                }
            }

            if (topCandidate === null || topCandidate.tagName === 'BODY') {
                topCandidate = document.createElement('DIV');
                topCandidate.innerHTML = document.body.innerHTML;
                document.body.innerHTML = '';
                document.body.appendChild(topCandidate);
            }

            return topCandidate;
        }

        // Main extraction logic
        try {
            document.body = cleanStyles(document.body);
            
            const articleTitle = getArticleTitle();
            const articleContent = grabArticle();
            
            if (!articleContent) {
                return { text: '', html: '', excerpt: '' };
            }

            // Clean up the content
            clean(articleContent, 'script');
            clean(articleContent, 'noscript');
            clean(articleContent, 'style');
            killBreaks(articleContent);

            const text = getInnerText(articleContent);
            const html = articleContent.innerHTML;
            const excerpt = text.substring(0, 300) + (text.length > 300 ? '...' : '');

            return {
                text: text.replace(config.normalize, ' ').trim(),
                html: html,
                excerpt: excerpt,
                title: articleTitle
            };
        } catch (error) {
            console.error('Readability extraction error:', error);
            return { text: '', html: '', excerpt: '' };
        }
    }

    async extractYouTubeContent(pageData, tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId },
                func: () => {
                    // Extract YouTube video information
                    const videoData = {};
                    
                    // Try to get video description
                    const descriptionElement = document.querySelector('#description-text, #meta-contents #description, .content-description');
                    if (descriptionElement) {
                        videoData.description = descriptionElement.textContent.trim();
                    }

                    // Try to get video duration
                    const durationElement = document.querySelector('.ytp-time-duration, .video-stream');
                    if (durationElement) {
                        videoData.duration = durationElement.textContent || durationElement.duration;
                    }

                    // Try to get view count
                    const viewsElement = document.querySelector('#info-text, .view-count');
                    if (viewsElement) {
                        videoData.views = viewsElement.textContent.trim();
                    }

                    // Try to get channel name
                    const channelElement = document.querySelector('#channel-name, .ytd-channel-name a');
                    if (channelElement) {
                        videoData.channel = channelElement.textContent.trim();
                    }

                    // Try to get upload date
                    const dateElement = document.querySelector('#info-strings yt-formatted-string, .date');
                    if (dateElement) {
                        videoData.uploadDate = dateElement.textContent.trim();
                    }

                    return videoData;
                }
            });

            const videoInfo = results?.[0]?.result || {};
            
            return {
                type: 'video',
                platform: 'youtube',
                videoId: pageData.metadata?.videoId,
                description: videoInfo.description || pageData.description,
                duration: videoInfo.duration,
                views: videoInfo.views,
                channel: videoInfo.channel || pageData.author,
                uploadDate: videoInfo.uploadDate,
                embedUrl: pageData.metadata?.embedUrl
            };
        } catch (error) {
            console.error('Error extracting YouTube content:', error);
            return { type: 'video', platform: 'youtube', error: error.message };
        }
    }

    async extractPDFContent(tabId) {
        // PDF content extraction would require additional libraries
        // For now, return basic PDF information
        return {
            type: 'document',
            format: 'pdf',
            extractable: false,
            note: 'PDF content extraction requires additional processing'
        };
    }

    async extractGenericContent(tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId },
                func: () => {
                    // Extract main content from generic pages
                    const contentSelectors = [
                        'main',
                        '[role="main"]',
                        '.main-content',
                        '.content',
                        '#content',
                        'article',
                        '.post-content',
                        '.entry-content'
                    ];

                    let content = '';
                    for (const selector of contentSelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            content = element.textContent.trim();
                            if (content.length > 100) break;
                        }
                    }

                    // Fallback to body content
                    if (!content || content.length < 100) {
                        content = document.body.textContent.trim();
                    }

                    return {
                        text: content.substring(0, 5000), // Limit to 5000 chars
                        excerpt: content.substring(0, 300) + (content.length > 300 ? '...' : '')
                    };
                }
            });

            return results?.[0]?.result || { text: '', excerpt: '' };
        } catch (error) {
            console.error('Error extracting generic content:', error);
            return { text: '', excerpt: '' };
        }
    }

    estimateReadingTime(text) {
        if (!text) return 0;
        const wordsPerMinute = 200;
        const wordCount = text.split(/\s+/).length;
        return Math.ceil(wordCount / wordsPerMinute);
    }

    countWords(text) {
        if (!text) return 0;
        return text.split(/\s+/).filter(word => word.length > 0).length;
    }

    async categorizeContent(pageData) {
        // Simple keyword-based categorization
        const categories = {
            'Technology': ['tech', 'software', 'programming', 'code', 'developer', 'ai', 'machine learning', 'javascript', 'python', 'react', 'api'],
            'Business': ['business', 'startup', 'entrepreneur', 'finance', 'investment', 'market', 'economy', 'company', 'revenue', 'profit'],
            'Entertainment': ['movie', 'music', 'game', 'entertainment', 'celebrity', 'film', 'tv', 'show', 'streaming', 'netflix'],
            'News': ['news', 'breaking', 'report', 'politics', 'government', 'election', 'policy', 'world', 'international'],
            'Education': ['education', 'learning', 'course', 'tutorial', 'guide', 'how to', 'university', 'school', 'study'],
            'Health': ['health', 'medical', 'fitness', 'wellness', 'diet', 'exercise', 'doctor', 'medicine', 'nutrition'],
            'Science': ['science', 'research', 'study', 'discovery', 'experiment', 'biology', 'chemistry', 'physics'],
            'Sports': ['sports', 'football', 'basketball', 'soccer', 'baseball', 'tennis', 'olympics', 'athlete', 'game'],
            'Travel': ['travel', 'vacation', 'trip', 'destination', 'hotel', 'flight', 'tourism', 'adventure'],
            'Food': ['food', 'recipe', 'cooking', 'restaurant', 'cuisine', 'chef', 'meal', 'ingredient']
        };

        const content = (pageData.title + ' ' + pageData.description + ' ' + (pageData.content?.text || '')).toLowerCase();
        
        let bestCategory = 'General';
        let maxScore = 0;

        for (const [category, keywords] of Object.entries(categories)) {
            let score = 0;
            for (const keyword of keywords) {
                const regex = new RegExp(keyword, 'gi');
                const matches = content.match(regex);
                if (matches) {
                    score += matches.length;
                }
            }
            
            if (score > maxScore) {
                maxScore = score;
                bestCategory = category;
            }
        }

        return {
            primary: bestCategory,
            confidence: Math.min(maxScore / 3, 1), // Normalize confidence score
            keywords: maxScore
        };
    }

    async extractTags(pageData) {
        const content = (pageData.title + ' ' + pageData.description + ' ' + (pageData.content?.text || '')).toLowerCase();
        
        // Extract potential tags using simple keyword extraction
        const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);
        
        const words = content.match(/\b\w{3,}\b/g) || [];
        const wordFreq = {};
        
        words.forEach(word => {
            if (!commonWords.has(word)) {
                wordFreq[word] = (wordFreq[word] || 0) + 1;
            }
        });

        // Get top 10 most frequent words as tags
        const tags = Object.entries(wordFreq)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([word]) => word);

        return tags;
    }

    async extractTopics(pageData) {
        // Simple topic extraction based on content analysis
        const content = pageData.content?.text || pageData.description || '';
        
        if (content.length < 100) {
            return [];
        }

        // Extract sentences and find key topics
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        const topics = [];

        // Look for sentences that might indicate main topics
        sentences.slice(0, 5).forEach(sentence => {
            const words = sentence.trim().split(/\s+/);
            if (words.length > 5 && words.length < 20) {
                topics.push(sentence.trim().substring(0, 100));
            }
        });

        return topics.slice(0, 3); // Return top 3 topics
    }

    async extractFavicon(tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId },
                func: () => {
                    // Try to find favicon
                    const faviconSelectors = [
                        'link[rel="icon"]',
                        'link[rel="shortcut icon"]',
                        'link[rel="apple-touch-icon"]',
                        'link[rel="apple-touch-icon-precomposed"]'
                    ];

                    for (const selector of faviconSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.href) {
                            return element.href;
                        }
                    }

                    // Fallback to default favicon location
                    return window.location.origin + '/favicon.ico';
                }
            });

            return results?.[0]?.result || '';
        } catch (error) {
            return '';
        }
    }

    async extractSiteName(tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId },
                func: () => {
                    // Try to get site name from meta tags
                    const siteNameMeta = document.querySelector('meta[property="og:site_name"]');
                    if (siteNameMeta) {
                        return siteNameMeta.content;
                    }

                    const applicationNameMeta = document.querySelector('meta[name="application-name"]');
                    if (applicationNameMeta) {
                        return applicationNameMeta.content;
                    }

                    // Fallback to hostname
                    return window.location.hostname.replace('www.', '');
                }
            });

            return results?.[0]?.result || '';
        } catch (error) {
            return '';
        }
    }
}

// Make ContentProcessor available globally
window.ContentProcessor = ContentProcessor;
