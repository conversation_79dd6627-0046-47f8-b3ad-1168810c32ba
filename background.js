// Background service worker for Chrome extension
class BackgroundService {
    constructor() {
        this.init();
    }

    init() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Handle extension startup
        chrome.runtime.onStartup.addListener(() => {
            this.handleStartup();
        });

        // Handle messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates for content type detection
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Handle storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    handleInstallation(details) {
        console.log('Page Saver extension installed:', details.reason);
        
        if (details.reason === 'install') {
            // First time installation
            this.initializeStorage();
            this.showWelcomeNotification();
        } else if (details.reason === 'update') {
            // Extension updated
            this.handleUpdate(details.previousVersion);
        }
    }

    handleStartup() {
        console.log('Page Saver extension started');
        // Perform any startup tasks
        this.cleanupOldData();
    }

    async initializeStorage() {
        try {
            // Initialize default storage structure
            const defaultData = {
                savedPages: [],
                settings: {
                    maxSavedItems: 1000,
                    autoCleanup: true,
                    cleanupDays: 30,
                    showNotifications: true
                },
                stats: {
                    totalSaved: 0,
                    lastCleanup: Date.now()
                }
            };

            // Only set if not already exists
            const existing = await chrome.storage.local.get(Object.keys(defaultData));
            const toSet = {};
            
            for (const [key, value] of Object.entries(defaultData)) {
                if (!(key in existing)) {
                    toSet[key] = value;
                }
            }

            if (Object.keys(toSet).length > 0) {
                await chrome.storage.local.set(toSet);
                console.log('Storage initialized with default data');
            }
        } catch (error) {
            console.error('Error initializing storage:', error);
        }
    }

    async handleUpdate(previousVersion) {
        console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
        
        try {
            // Perform any necessary data migrations
            await this.migrateData(previousVersion);
            
            // Show update notification
            if (await this.getSetting('showNotifications')) {
                this.showUpdateNotification();
            }
        } catch (error) {
            console.error('Error handling update:', error);
        }
    }

    async migrateData(previousVersion) {
        // Handle data migration between versions
        const currentVersion = chrome.runtime.getManifest().version;
        
        // Example migration logic
        if (this.compareVersions(previousVersion, '1.0.0') < 0) {
            // Migrate from pre-1.0.0 format
            console.log('Migrating data to version 1.0.0 format');
            // Add migration logic here
        }
    }

    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            
            if (aPart < bPart) return -1;
            if (aPart > bPart) return 1;
        }
        
        return 0;
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'savePage':
                    const result = await this.savePage(request.pageData);
                    sendResponse({ success: true, data: result });
                    break;

                case 'getSavedPages':
                    const pages = await this.getSavedPages();
                    sendResponse({ success: true, data: pages });
                    break;

                case 'deletePage':
                    await this.deletePage(request.pageId);
                    sendResponse({ success: true });
                    break;

                case 'clearAllPages':
                    await this.clearAllPages();
                    sendResponse({ success: true });
                    break;

                case 'getStats':
                    const stats = await this.getStats();
                    sendResponse({ success: true, data: stats });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Handle tab updates if needed
        if (changeInfo.status === 'complete' && tab.url) {
            // Tab finished loading, could trigger content analysis
            this.onTabLoaded(tabId, tab);
        }
    }

    async onTabLoaded(tabId, tab) {
        // Inject content script automatically for better user experience
        if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://') && !tab.url.startsWith('moz-extension://')) {
            try {
                // Check if we can inject scripts on this tab
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: () => {
                        // Simple function to test if injection works
                        return true;
                    }
                });

                // If test injection worked, inject our content script
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });
            } catch (error) {
                // Ignore errors (script might already be injected or page doesn't allow injection)
                console.log('Content script injection failed for tab:', tabId, error.message);
            }
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'local') {
            // Handle storage changes
            if (changes.savedPages) {
                this.updateStats();
            }
        }
    }

    async savePage(pageData) {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            
            // Check if page already exists
            const existingIndex = savedPages.findIndex(page => page.url === pageData.url);
            
            if (existingIndex !== -1) {
                // Update existing page
                savedPages[existingIndex] = { ...pageData, timestamp: Date.now() };
            } else {
                // Add new page
                savedPages.unshift({ ...pageData, timestamp: Date.now() });
            }

            // Enforce max items limit
            const maxItems = await this.getSetting('maxSavedItems');
            if (savedPages.length > maxItems) {
                savedPages.splice(maxItems);
            }

            await chrome.storage.local.set({ savedPages });
            await this.updateStats();

            return { saved: true, total: savedPages.length };
        } catch (error) {
            console.error('Error saving page:', error);
            throw error;
        }
    }

    async getSavedPages() {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            return savedPages;
        } catch (error) {
            console.error('Error getting saved pages:', error);
            throw error;
        }
    }

    async deletePage(pageId) {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const filteredPages = savedPages.filter((_, index) => index !== pageId);
            await chrome.storage.local.set({ savedPages: filteredPages });
            await this.updateStats();
        } catch (error) {
            console.error('Error deleting page:', error);
            throw error;
        }
    }

    async clearAllPages() {
        try {
            await chrome.storage.local.set({ savedPages: [] });
            await this.updateStats();
        } catch (error) {
            console.error('Error clearing pages:', error);
            throw error;
        }
    }

    async getSetting(key) {
        try {
            const { settings = {} } = await chrome.storage.local.get(['settings']);
            return settings[key];
        } catch (error) {
            console.error('Error getting setting:', error);
            return null;
        }
    }

    async updateStats() {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const stats = {
                totalSaved: savedPages.length,
                lastUpdate: Date.now()
            };
            await chrome.storage.local.set({ stats });
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    async getStats() {
        try {
            const { stats = {} } = await chrome.storage.local.get(['stats']);
            return stats;
        } catch (error) {
            console.error('Error getting stats:', error);
            return {};
        }
    }

    async cleanupOldData() {
        try {
            const autoCleanup = await this.getSetting('autoCleanup');
            if (!autoCleanup) return;

            const cleanupDays = await this.getSetting('cleanupDays') || 30;
            const cutoffTime = Date.now() - (cleanupDays * 24 * 60 * 60 * 1000);

            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const filteredPages = savedPages.filter(page => page.timestamp > cutoffTime);

            if (filteredPages.length !== savedPages.length) {
                await chrome.storage.local.set({ savedPages: filteredPages });
                console.log(`Cleaned up ${savedPages.length - filteredPages.length} old pages`);
            }
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    showWelcomeNotification() {
        // Optional: Show welcome notification
        console.log('Welcome to Page Saver!');
    }

    showUpdateNotification() {
        // Optional: Show update notification
        console.log('Page Saver has been updated!');
    }
}

// Initialize background service
new BackgroundService();
