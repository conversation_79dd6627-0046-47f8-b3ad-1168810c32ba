// Background service worker for Chrome extension
class BackgroundService {
    constructor() {
        this.contentProcessor = null;
        this.processingQueue = new Map();
        this.init();
    }

    init() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Handle extension startup
        chrome.runtime.onStartup.addListener(() => {
            this.handleStartup();
        });

        // Handle messages from content scripts and popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates for content type detection
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Handle storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });

        // Initialize content processor
        this.initializeContentProcessor();
    }

    async initializeContentProcessor() {
        try {
            // Since we can't use ES6 imports in service workers easily,
            // we'll inject the content processor when needed
            this.contentProcessor = {
                processContent: async (pageData, tabId) => {
                    // Inject and execute content processor
                    const results = await chrome.scripting.executeScript({
                        target: { tabId },
                        files: ['content-processor.js']
                    });

                    // Execute processing
                    const processResults = await chrome.scripting.executeScript({
                        target: { tabId },
                        func: async (pageData) => {
                            if (window.ContentProcessor) {
                                const processor = new window.ContentProcessor();
                                return await processor.processContent(pageData, null);
                            }
                            return pageData;
                        },
                        args: [pageData]
                    });

                    return processResults?.[0]?.result || pageData;
                }
            };
        } catch (error) {
            console.error('Failed to initialize content processor:', error);
            this.contentProcessor = null;
        }
    }

    handleInstallation(details) {
        console.log('Page Saver extension installed:', details.reason);
        
        if (details.reason === 'install') {
            // First time installation
            this.initializeStorage();
            this.showWelcomeNotification();
        } else if (details.reason === 'update') {
            // Extension updated
            this.handleUpdate(details.previousVersion);
        }
    }

    handleStartup() {
        console.log('Page Saver extension started');
        // Perform any startup tasks
        this.cleanupOldData();
    }

    async initializeStorage() {
        try {
            // Initialize default storage structure
            const defaultData = {
                savedPages: [],
                settings: {
                    maxSavedItems: 1000,
                    autoCleanup: true,
                    cleanupDays: 30,
                    showNotifications: true
                },
                stats: {
                    totalSaved: 0,
                    lastCleanup: Date.now()
                }
            };

            // Only set if not already exists
            const existing = await chrome.storage.local.get(Object.keys(defaultData));
            const toSet = {};
            
            for (const [key, value] of Object.entries(defaultData)) {
                if (!(key in existing)) {
                    toSet[key] = value;
                }
            }

            if (Object.keys(toSet).length > 0) {
                await chrome.storage.local.set(toSet);
                console.log('Storage initialized with default data');
            }
        } catch (error) {
            console.error('Error initializing storage:', error);
        }
    }

    async handleUpdate(previousVersion) {
        console.log(`Extension updated from ${previousVersion} to ${chrome.runtime.getManifest().version}`);
        
        try {
            // Perform any necessary data migrations
            await this.migrateData(previousVersion);
            
            // Show update notification
            if (await this.getSetting('showNotifications')) {
                this.showUpdateNotification();
            }
        } catch (error) {
            console.error('Error handling update:', error);
        }
    }

    async migrateData(previousVersion) {
        // Handle data migration between versions
        const currentVersion = chrome.runtime.getManifest().version;
        
        // Example migration logic
        if (this.compareVersions(previousVersion, '1.0.0') < 0) {
            // Migrate from pre-1.0.0 format
            console.log('Migrating data to version 1.0.0 format');
            // Add migration logic here
        }
    }

    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            
            if (aPart < bPart) return -1;
            if (aPart > bPart) return 1;
        }
        
        return 0;
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'savePage':
                    const result = await this.savePage(request.pageData, sender.tab?.id);
                    sendResponse({ success: true, data: result });
                    break;

                case 'processContent':
                    const processedData = await this.processContent(request.pageData, sender.tab?.id);
                    sendResponse({ success: true, data: processedData });
                    break;

                case 'getSavedPages':
                    const pages = await this.getSavedPages(request.filters);
                    sendResponse({ success: true, data: pages });
                    break;

                case 'searchPages':
                    const searchResults = await this.searchPages(request.query, request.filters);
                    sendResponse({ success: true, data: searchResults });
                    break;

                case 'deletePage':
                    await this.deletePage(request.pageId);
                    sendResponse({ success: true });
                    break;

                case 'clearAllPages':
                    await this.clearAllPages();
                    sendResponse({ success: true });
                    break;

                case 'getStats':
                    const stats = await this.getStats();
                    sendResponse({ success: true, data: stats });
                    break;

                case 'getCategories':
                    const categories = await this.getCategories();
                    sendResponse({ success: true, data: categories });
                    break;

                case 'updatePageData':
                    await this.updatePageData(request.pageId, request.updates);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Handle tab updates if needed
        if (changeInfo.status === 'complete' && tab.url) {
            // Tab finished loading, could trigger content analysis
            this.onTabLoaded(tabId, tab);
        }
    }

    async onTabLoaded(tabId, tab) {
        // Inject content script automatically for better user experience
        if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://') && !tab.url.startsWith('moz-extension://')) {
            try {
                // Check if we can inject scripts on this tab
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    func: () => {
                        // Simple function to test if injection works
                        return true;
                    }
                });

                // If test injection worked, inject our content script
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });
            } catch (error) {
                // Ignore errors (script might already be injected or page doesn't allow injection)
                console.log('Content script injection failed for tab:', tabId, error.message);
            }
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'local') {
            // Handle storage changes
            if (changes.savedPages) {
                this.updateStats();
            }
        }
    }

    async savePage(pageData, tabId) {
        try {
            // Start background processing if content processor is available
            let processedData = pageData;
            if (this.contentProcessor && tabId) {
                try {
                    // Queue for background processing
                    const processingId = `${Date.now()}_${Math.random()}`;
                    this.processingQueue.set(processingId, {
                        pageData,
                        tabId,
                        status: 'processing',
                        startTime: Date.now()
                    });

                    // Process content in background
                    this.processContentInBackground(processingId, pageData, tabId);

                    // For now, save the basic data immediately
                    processedData = {
                        ...pageData,
                        processingStatus: 'queued',
                        processingId: processingId
                    };
                } catch (error) {
                    console.error('Error starting background processing:', error);
                }
            }

            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);

            // Check if page already exists
            const existingIndex = savedPages.findIndex(page => page.url === processedData.url);

            if (existingIndex !== -1) {
                // Update existing page
                savedPages[existingIndex] = {
                    ...savedPages[existingIndex],
                    ...processedData,
                    timestamp: Date.now(),
                    lastUpdated: Date.now()
                };
            } else {
                // Add new page with enhanced structure
                const enhancedPageData = {
                    id: `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    ...processedData,
                    timestamp: Date.now(),
                    lastUpdated: Date.now(),
                    version: '2.0'
                };
                savedPages.unshift(enhancedPageData);
            }

            // Enforce max items limit
            const maxItems = await this.getSetting('maxSavedItems') || 1000;
            if (savedPages.length > maxItems) {
                savedPages.splice(maxItems);
            }

            await chrome.storage.local.set({ savedPages });
            await this.updateStats();

            return {
                saved: true,
                total: savedPages.length,
                processingStatus: processedData.processingStatus || 'complete'
            };
        } catch (error) {
            console.error('Error saving page:', error);
            throw error;
        }
    }

    async processContentInBackground(processingId, pageData, tabId) {
        try {
            // Update processing status
            const queueItem = this.processingQueue.get(processingId);
            if (queueItem) {
                queueItem.status = 'processing';
            }

            // Process content with enhanced capabilities
            const processedData = await this.contentProcessor.processContent(pageData, tabId);

            // Update the saved page with processed data
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const pageIndex = savedPages.findIndex(page =>
                page.url === pageData.url || page.processingId === processingId
            );

            if (pageIndex !== -1) {
                savedPages[pageIndex] = {
                    ...savedPages[pageIndex],
                    ...processedData,
                    processingStatus: 'complete',
                    lastUpdated: Date.now()
                };

                // Remove processing ID as it's no longer needed
                delete savedPages[pageIndex].processingId;

                await chrome.storage.local.set({ savedPages });

                // Show notification if enabled
                const showNotifications = await this.getSetting('showNotifications');
                if (showNotifications) {
                    this.showProcessingCompleteNotification(processedData);
                }
            }

            // Clean up processing queue
            this.processingQueue.delete(processingId);

        } catch (error) {
            console.error('Error in background processing:', error);

            // Update processing status to error
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const pageIndex = savedPages.findIndex(page =>
                page.processingId === processingId
            );

            if (pageIndex !== -1) {
                savedPages[pageIndex].processingStatus = 'error';
                savedPages[pageIndex].processingError = error.message;
                await chrome.storage.local.set({ savedPages });
            }

            this.processingQueue.delete(processingId);
        }
    }

    async processContent(pageData, tabId) {
        if (!this.contentProcessor) {
            throw new Error('Content processor not available');
        }

        return await this.contentProcessor.processContent(pageData, tabId);
    }

    async getSavedPages(filters = {}) {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);

            let filteredPages = [...savedPages];

            // Apply filters
            if (filters.category) {
                filteredPages = filteredPages.filter(page =>
                    page.category?.primary === filters.category
                );
            }

            if (filters.type) {
                filteredPages = filteredPages.filter(page =>
                    page.type?.type === filters.type
                );
            }

            if (filters.dateRange) {
                const { start, end } = filters.dateRange;
                filteredPages = filteredPages.filter(page => {
                    const pageDate = new Date(page.timestamp);
                    return pageDate >= new Date(start) && pageDate <= new Date(end);
                });
            }

            if (filters.tags && filters.tags.length > 0) {
                filteredPages = filteredPages.filter(page =>
                    page.tags && page.tags.some(tag => filters.tags.includes(tag))
                );
            }

            // Sort by timestamp (newest first) unless specified otherwise
            const sortBy = filters.sortBy || 'timestamp';
            const sortOrder = filters.sortOrder || 'desc';

            filteredPages.sort((a, b) => {
                let aVal = a[sortBy];
                let bVal = b[sortBy];

                if (sortBy === 'title') {
                    aVal = aVal?.toLowerCase() || '';
                    bVal = bVal?.toLowerCase() || '';
                }

                if (sortOrder === 'desc') {
                    return bVal > aVal ? 1 : -1;
                } else {
                    return aVal > bVal ? 1 : -1;
                }
            });

            return filteredPages;
        } catch (error) {
            console.error('Error getting saved pages:', error);
            throw error;
        }
    }

    async searchPages(query, filters = {}) {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);

            if (!query || query.trim().length === 0) {
                return this.getSavedPages(filters);
            }

            const searchTerms = query.toLowerCase().split(/\s+/);

            const searchResults = savedPages.filter(page => {
                const searchableText = [
                    page.title,
                    page.description,
                    page.content?.text,
                    page.content?.excerpt,
                    page.author,
                    page.siteName,
                    ...(page.tags || []),
                    ...(page.topics || [])
                ].join(' ').toLowerCase();

                return searchTerms.every(term => searchableText.includes(term));
            });

            // Apply additional filters
            let filteredResults = searchResults;
            if (filters.category) {
                filteredResults = filteredResults.filter(page =>
                    page.category?.primary === filters.category
                );
            }

            if (filters.type) {
                filteredResults = filteredResults.filter(page =>
                    page.type?.type === filters.type
                );
            }

            // Sort by relevance (number of matching terms) and then by date
            filteredResults.sort((a, b) => {
                const aText = [a.title, a.description].join(' ').toLowerCase();
                const bText = [b.title, b.description].join(' ').toLowerCase();

                const aMatches = searchTerms.reduce((count, term) => {
                    return count + (aText.split(term).length - 1);
                }, 0);

                const bMatches = searchTerms.reduce((count, term) => {
                    return count + (bText.split(term).length - 1);
                }, 0);

                if (aMatches !== bMatches) {
                    return bMatches - aMatches; // More matches first
                }

                return b.timestamp - a.timestamp; // Then by date
            });

            return filteredResults;
        } catch (error) {
            console.error('Error searching pages:', error);
            throw error;
        }
    }

    async deletePage(pageId) {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const filteredPages = savedPages.filter((_, index) => index !== pageId);
            await chrome.storage.local.set({ savedPages: filteredPages });
            await this.updateStats();
        } catch (error) {
            console.error('Error deleting page:', error);
            throw error;
        }
    }

    async clearAllPages() {
        try {
            await chrome.storage.local.set({ savedPages: [] });
            await this.updateStats();
        } catch (error) {
            console.error('Error clearing pages:', error);
            throw error;
        }
    }

    async getSetting(key) {
        try {
            const { settings = {} } = await chrome.storage.local.get(['settings']);
            return settings[key];
        } catch (error) {
            console.error('Error getting setting:', error);
            return null;
        }
    }

    async updateStats() {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const stats = {
                totalSaved: savedPages.length,
                lastUpdate: Date.now()
            };
            await chrome.storage.local.set({ stats });
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    async getStats() {
        try {
            const { savedPages = [], stats = {} } = await chrome.storage.local.get(['savedPages', 'stats']);

            // Calculate enhanced statistics
            const enhancedStats = {
                ...stats,
                totalSaved: savedPages.length,
                lastUpdate: Date.now()
            };

            // Category breakdown
            const categoryStats = {};
            const typeStats = {};
            let totalReadingTime = 0;
            let totalWordCount = 0;

            savedPages.forEach(page => {
                // Category stats
                const category = page.category?.primary || 'Uncategorized';
                categoryStats[category] = (categoryStats[category] || 0) + 1;

                // Type stats
                const type = page.type?.type || 'unknown';
                typeStats[type] = (typeStats[type] || 0) + 1;

                // Reading time and word count
                if (page.readingTime) {
                    totalReadingTime += page.readingTime;
                }
                if (page.wordCount) {
                    totalWordCount += page.wordCount;
                }
            });

            enhancedStats.categoryBreakdown = categoryStats;
            enhancedStats.typeBreakdown = typeStats;
            enhancedStats.totalReadingTime = totalReadingTime;
            enhancedStats.totalWordCount = totalWordCount;
            enhancedStats.averageReadingTime = savedPages.length > 0 ? Math.round(totalReadingTime / savedPages.length) : 0;

            return enhancedStats;
        } catch (error) {
            console.error('Error getting stats:', error);
            return {};
        }
    }

    async getCategories() {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);

            const categories = new Set();
            const tags = new Set();
            const types = new Set();

            savedPages.forEach(page => {
                if (page.category?.primary) {
                    categories.add(page.category.primary);
                }
                if (page.tags) {
                    page.tags.forEach(tag => tags.add(tag));
                }
                if (page.type?.type) {
                    types.add(page.type.type);
                }
            });

            return {
                categories: Array.from(categories).sort(),
                tags: Array.from(tags).sort(),
                types: Array.from(types).sort()
            };
        } catch (error) {
            console.error('Error getting categories:', error);
            return { categories: [], tags: [], types: [] };
        }
    }

    async updatePageData(pageId, updates) {
        try {
            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const pageIndex = savedPages.findIndex(page => page.id === pageId);

            if (pageIndex !== -1) {
                savedPages[pageIndex] = {
                    ...savedPages[pageIndex],
                    ...updates,
                    lastUpdated: Date.now()
                };

                await chrome.storage.local.set({ savedPages });
                await this.updateStats();
            }
        } catch (error) {
            console.error('Error updating page data:', error);
            throw error;
        }
    }

    async cleanupOldData() {
        try {
            const autoCleanup = await this.getSetting('autoCleanup');
            if (!autoCleanup) return;

            const cleanupDays = await this.getSetting('cleanupDays') || 30;
            const cutoffTime = Date.now() - (cleanupDays * 24 * 60 * 60 * 1000);

            const { savedPages = [] } = await chrome.storage.local.get(['savedPages']);
            const filteredPages = savedPages.filter(page => page.timestamp > cutoffTime);

            if (filteredPages.length !== savedPages.length) {
                await chrome.storage.local.set({ savedPages: filteredPages });
                console.log(`Cleaned up ${savedPages.length - filteredPages.length} old pages`);
            }
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    showWelcomeNotification() {
        // Show welcome notification
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Page Saver Installed!',
            message: 'Welcome to Page Saver! Start saving and organizing your favorite web content.'
        });
    }

    showUpdateNotification() {
        // Show update notification
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Page Saver Updated!',
            message: 'New features: Enhanced content extraction, categorization, and search!'
        });
    }

    showProcessingCompleteNotification(processedData) {
        const category = processedData.category?.primary || 'General';
        const readingTime = processedData.readingTime ? ` • ${processedData.readingTime} min read` : '';

        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: 'Content Processing Complete',
            message: `"${processedData.title}" categorized as ${category}${readingTime}`
        });
    }
}

// Initialize background service
new BackgroundService();
