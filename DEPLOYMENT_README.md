# 🚀 AI Podcast Generation API - Deployment Guide

A production-ready Vercel serverless function that generates NotebookLM-quality AI podcasts from saved web content using GPT-4 and ElevenLabs.

## 📋 Prerequisites

1. **Vercel Account** - Sign up at [vercel.com](https://vercel.com)
2. **OpenAI API Key** - Get from [platform.openai.com](https://platform.openai.com/api-keys)
   - Requires paid account with GPT-4 access
3. **ElevenLabs API Key** - Get from [elevenlabs.io](https://elevenlabs.io/app/speech-synthesis)
   - Requires paid plan for longer audio generation
4. **Node.js 18+** - For local development

## 🛠️ Quick Deploy to Vercel

### Option 1: One-Click Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/podcast-api)

### Option 2: Manual Deploy

1. **Clone/Create Project**
   ```bash
   mkdir podcast-api
   cd podcast-api
   # Copy all the files from this directory
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Set Environment Variables**
   ```bash
   # Install Vercel CLI
   npm i -g vercel

   # Login to Vercel
   vercel login

   # Add environment variables
   vercel env add OPENAI_API_KEY
   # Paste your OpenAI API key when prompted

   vercel env add ELEVENLABS_API_KEY
   # Paste your ElevenLabs API key when prompted
   ```

4. **Deploy**
   ```bash
   vercel --prod
   ```

5. **Get Your API URL**
   After deployment, you'll get a URL like:
   ```
   https://your-project-name.vercel.app
   ```

## 🔧 Local Development

1. **Create `.env.local`**
   ```bash
   OPENAI_API_KEY=sk-your-openai-key-here
   ELEVENLABS_API_KEY=your-elevenlabs-key-here
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Test the API**
   ```bash
   curl -X POST http://localhost:3000/api/generate-podcast \
     -H "Content-Type: application/json" \
     -d '{
       "savedItems": [
         {
           "title": "Test Article",
           "description": "A test article about AI",
           "url": "https://example.com"
         }
       ],
       "hostA": "alex",
       "hostB": "sam",
       "targetLength": 5
     }'
   ```

## 🔌 Chrome Extension Integration

Update your extension's popup.js to use the deployed API:

```javascript
// Replace the generatePodcast method in popup.js
async generatePodcast() {
    const API_BASE_URL = 'https://your-project-name.vercel.app';
    
    const hostA = document.getElementById('hostA').value;
    const hostB = document.getElementById('hostB').value;
    const targetLength = parseInt(document.getElementById('podcastLength').value);
    const focusArea = document.getElementById('podcastFocus').value;
    const style = document.getElementById('podcastStyle').value;

    if (hostA === hostB) {
        this.showError('Please select different personalities for each host');
        return;
    }

    if (this.savedItems.length === 0) {
        this.showError('No saved content available for podcast generation');
        return;
    }

    try {
        this.showPodcastProgress();

        // Filter content based on focus area
        let contentToUse = this.savedItems;
        if (focusArea !== 'general') {
            contentToUse = this.savedItems.filter(item => 
                item.category?.primary?.toLowerCase().includes(focusArea.toLowerCase()) ||
                item.type?.type === focusArea
            );
        }

        if (contentToUse.length === 0) {
            this.showError(`No content found for focus area: ${focusArea}`);
            this.hidePodcastProgress();
            return;
        }

        // Call the API
        const response = await fetch(`${API_BASE_URL}/api/generate-podcast`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                savedItems: contentToUse.slice(0, 6), // Limit to 6 items
                hostA,
                hostB,
                targetLength,
                focusArea,
                style
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API Error: ${response.status} - ${errorText}`);
        }

        // Get audio blob
        const audioBlob = await response.blob();

        // Show result
        this.showPodcastResult({
            audioBlob,
            script: 'Generated via API',
            metadata: {
                hostA,
                hostB,
                targetLength,
                contentItems: contentToUse.length,
                generatedAt: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('Error generating podcast:', error);
        this.showError(`Podcast generation failed: ${error.message}`);
        this.hidePodcastProgress();
    }
}
```

## 🔒 Security & Rate Limiting

The API includes:

- **CORS Protection**: Only allows requests from Chrome/Firefox extensions
- **Rate Limiting**: 5 requests per minute per IP
- **Input Validation**: Validates all request parameters
- **Error Handling**: Graceful error responses
- **Environment Variables**: Secure API key storage

## 💰 Cost Estimation

### Per 10-minute podcast:
- **OpenAI GPT-4**: ~$0.10-0.50 (depending on content complexity)
- **ElevenLabs**: ~$0.30-1.00 (depending on plan)
- **Vercel**: Free tier covers ~1000 podcasts/month

### Monthly costs (100 podcasts):
- **Total**: ~$40-150/month
- **Vercel Pro** (if needed): $20/month

## 📊 Monitoring & Analytics

1. **Vercel Dashboard**: Monitor function invocations, errors, and performance
2. **OpenAI Usage**: Track API usage at platform.openai.com
3. **ElevenLabs Usage**: Monitor character usage at elevenlabs.io

## 🐛 Troubleshooting

### Common Issues:

**"Server configuration error"**
- Check that environment variables are set correctly
- Verify API keys are valid

**"Rate limit exceeded"**
- Wait 1 minute between requests
- Consider implementing user authentication for higher limits

**"OpenAI API error"**
- Check API key has GPT-4 access
- Verify sufficient credits/quota

**"ElevenLabs API error"**
- Check API key is valid
- Verify sufficient character quota

**"CORS error"**
- Ensure requests are coming from a browser extension
- Check origin headers

### Debug Mode:

Enable verbose logging by adding to your environment:
```bash
vercel env add DEBUG true
```

## 🚀 Production Optimizations

### Performance:
- Function runs in ~30-120 seconds depending on content length
- Automatic scaling handles concurrent requests
- Audio streaming for faster response times

### Reliability:
- Retry logic for API failures
- Graceful degradation if one segment fails
- Comprehensive error handling

### Monitoring:
```bash
# View function logs
vercel logs

# Monitor performance
vercel analytics
```

## 🔄 Updates & Maintenance

1. **Update Dependencies**
   ```bash
   npm update
   vercel --prod
   ```

2. **Monitor API Changes**
   - OpenAI API updates
   - ElevenLabs API changes
   - Vercel platform updates

3. **Backup Strategy**
   - Environment variables are backed up in Vercel
   - Code is version controlled
   - No persistent data to backup

## 📞 Support

If you encounter issues:

1. Check Vercel function logs: `vercel logs`
2. Verify environment variables: `vercel env ls`
3. Test API endpoints locally: `npm run dev`
4. Check API provider status pages

## 🎯 Next Steps

After deployment:

1. **Test the API** with your Chrome extension
2. **Monitor usage** and costs
3. **Optimize** based on real usage patterns
4. **Scale** as needed with Vercel Pro features

Your API will be production-ready and handle real podcast generation workloads!
