{"name": "podcast-generation-api", "version": "1.0.0", "description": "Serverless API for generating AI podcasts from saved web content", "main": "index.js", "scripts": {"dev": "vercel dev", "build": "tsc", "deploy": "vercel --prod", "test": "jest"}, "dependencies": {"next": "^14.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "@vercel/node": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["podcast", "ai", "text-to-speech", "openai", "elevenlabs", "vercel", "serverless"], "author": "Your Name", "license": "MIT"}