<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator for Page Saver Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        .icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .icon-16 { width: 16px; height: 16px; font-size: 8px; }
        .icon-32 { width: 32px; height: 32px; font-size: 14px; }
        .icon-48 { width: 48px; height: 48px; font-size: 20px; }
        .icon-128 { width: 128px; height: 128px; font-size: 48px; }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .step {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .step strong {
            color: #2196f3;
        }
        button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        .download-section {
            text-align: center;
            margin: 30px 0;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Page Saver Extension - Icon Generator</h1>
        
        <div class="instructions">
            <h3>Quick Fix for Chrome Extension Loading</h3>
            <p>Your extension failed to load because it's missing icon files. Here are two solutions:</p>
        </div>

        <div class="icon-preview">
            <div class="icon-item">
                <div class="icon icon-16">PS</div>
                <div>16x16</div>
            </div>
            <div class="icon-item">
                <div class="icon icon-32">PS</div>
                <div>32x32</div>
            </div>
            <div class="icon-item">
                <div class="icon icon-48">📚</div>
                <div>48x48</div>
            </div>
            <div class="icon-item">
                <div class="icon icon-128">📚</div>
                <div>128x128</div>
            </div>
        </div>

        <div class="download-section">
            <h3>Option 1: Generate Simple Icons (Recommended)</h3>
            <p>Click the button below to generate and download basic PNG icons:</p>
            <button onclick="generateIcons()">Generate & Download Icons</button>
            <div id="canvasContainer" style="display: none;">
                <canvas id="canvas16" width="16" height="16"></canvas>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <canvas id="canvas128" width="128" height="128"></canvas>
            </div>
        </div>

        <div class="instructions">
            <h3>Option 2: Manual Steps</h3>
            <div class="step">
                <strong>Step 1:</strong> Create a simple 128x128 pixel image with any image editor (Paint, GIMP, Photoshop, etc.)
            </div>
            <div class="step">
                <strong>Step 2:</strong> Use a solid color background (like #667eea) with white text "PS" or a book emoji 📚
            </div>
            <div class="step">
                <strong>Step 3:</strong> Save as PNG and resize to create: icon16.png, icon32.png, icon48.png, icon128.png
            </div>
            <div class="step">
                <strong>Step 4:</strong> Place all PNG files in the "icons" folder of your extension
            </div>
            <div class="step">
                <strong>Step 5:</strong> Reload the extension in Chrome
            </div>
        </div>

        <div class="instructions">
            <h3>Option 3: Use the Fixed Manifest (Easiest)</h3>
            <p>I've already updated your manifest.json to work without icons. Just reload the extension in Chrome and it should work!</p>
        </div>
    </div>

    <script>
        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            const canvasContainer = document.getElementById('canvasContainer');
            canvasContainer.style.display = 'block';
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                
                // Create gradient background
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);
                
                // Add text or emoji
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                if (size <= 32) {
                    ctx.font = `bold ${size * 0.5}px Arial`;
                    ctx.fillText('PS', size/2, size/2);
                } else {
                    ctx.font = `${size * 0.6}px Arial`;
                    ctx.fillText('📚', size/2, size/2);
                }
                
                // Create download link
                canvas.toBlob(blob => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `icon${size}.png`;
                    a.click();
                    URL.revokeObjectURL(url);
                });
            });
            
            alert('Icons generated! Check your downloads folder for icon16.png, icon32.png, icon48.png, and icon128.png. Place these files in your extension\'s "icons" folder.');
        }
    </script>
</body>
</html>
