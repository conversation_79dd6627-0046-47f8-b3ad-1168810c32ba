// Content script for page analysis and data extraction
class PageAnalyzer {
    constructor() {
        this.pageData = null;
        this.init();
    }

    init() {
        // Wait for page to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.analyzePage());
        } else {
            this.analyzePage();
        }
    }

    analyzePage() {
        this.pageData = {
            url: window.location.href,
            title: this.extractTitle(),
            type: this.detectContentType(),
            description: this.extractDescription(),
            author: this.extractAuthor(),
            publishDate: this.extractPublishDate(),
            thumbnail: this.extractThumbnail(),
            metadata: this.extractMetadata(),
            timestamp: Date.now()
        };

        // Store in session for quick access
        sessionStorage.setItem('pageAnalyzerData', JSON.stringify(this.pageData));
    }

    extractTitle() {
        // Try multiple sources for title
        const selectors = [
            'h1',
            '[property="og:title"]',
            '[name="twitter:title"]',
            'title',
            '.entry-title',
            '.post-title',
            '.article-title'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const title = element.getAttribute('content') || element.textContent;
                if (title && title.trim().length > 0) {
                    return title.trim();
                }
            }
        }

        return document.title || 'Untitled Page';
    }

    detectContentType() {
        const url = window.location.href.toLowerCase();
        const hostname = window.location.hostname.toLowerCase();

        // YouTube detection
        if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
            return {
                type: 'video',
                subtype: 'youtube',
                icon: '🎥',
                label: 'YouTube Video'
            };
        }

        // PDF detection
        if (url.includes('.pdf') || document.contentType === 'application/pdf') {
            return {
                type: 'document',
                subtype: 'pdf',
                icon: '📄',
                label: 'PDF Document'
            };
        }

        // News/Article detection
        const articleSelectors = [
            'article',
            '[role="article"]',
            '.post',
            '.entry',
            '.article',
            '[property="og:type"][content="article"]'
        ];

        if (articleSelectors.some(selector => document.querySelector(selector))) {
            return {
                type: 'article',
                subtype: 'news',
                icon: '📰',
                label: 'Article'
            };
        }

        // Blog detection
        const blogIndicators = [
            '.blog',
            '.post-content',
            '.entry-content',
            '[class*="blog"]'
        ];

        if (blogIndicators.some(selector => document.querySelector(selector))) {
            return {
                type: 'article',
                subtype: 'blog',
                icon: '📝',
                label: 'Blog Post'
            };
        }

        // E-commerce detection
        const ecommerceSelectors = [
            '[itemtype*="Product"]',
            '.product',
            '.item',
            '[data-product]'
        ];

        if (ecommerceSelectors.some(selector => document.querySelector(selector))) {
            return {
                type: 'product',
                subtype: 'ecommerce',
                icon: '🛒',
                label: 'Product Page'
            };
        }

        // Social media detection
        if (hostname.includes('twitter.com') || hostname.includes('x.com')) {
            return {
                type: 'social',
                subtype: 'twitter',
                icon: '🐦',
                label: 'Twitter/X Post'
            };
        }

        if (hostname.includes('linkedin.com')) {
            return {
                type: 'social',
                subtype: 'linkedin',
                icon: '💼',
                label: 'LinkedIn Post'
            };
        }

        // Default webpage
        return {
            type: 'webpage',
            subtype: 'general',
            icon: '🌐',
            label: 'Web Page'
        };
    }

    extractDescription() {
        const selectors = [
            '[property="og:description"]',
            '[name="description"]',
            '[name="twitter:description"]',
            '.excerpt',
            '.summary',
            '.description'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const description = element.getAttribute('content') || element.textContent;
                if (description && description.trim().length > 0) {
                    return description.trim().substring(0, 300);
                }
            }
        }

        // Fallback: extract from first paragraph
        const firstParagraph = document.querySelector('p');
        if (firstParagraph && firstParagraph.textContent.trim().length > 50) {
            return firstParagraph.textContent.trim().substring(0, 300);
        }

        return '';
    }

    extractAuthor() {
        const selectors = [
            '[property="og:author"]',
            '[name="author"]',
            '[rel="author"]',
            '.author',
            '.byline',
            '[class*="author"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const author = element.getAttribute('content') || element.textContent;
                if (author && author.trim().length > 0) {
                    return author.trim();
                }
            }
        }

        return '';
    }

    extractPublishDate() {
        const selectors = [
            '[property="article:published_time"]',
            '[name="publish_date"]',
            'time[datetime]',
            '.publish-date',
            '.date'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const date = element.getAttribute('content') || 
                           element.getAttribute('datetime') || 
                           element.textContent;
                if (date && date.trim().length > 0) {
                    return new Date(date.trim()).toISOString();
                }
            }
        }

        return '';
    }

    extractThumbnail() {
        const selectors = [
            '[property="og:image"]',
            '[name="twitter:image"]',
            '.featured-image img',
            '.thumbnail img',
            'article img:first-of-type'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const src = element.getAttribute('content') || element.src;
                if (src && src.startsWith('http')) {
                    return src;
                }
            }
        }

        return '';
    }

    extractMetadata() {
        const metadata = {};

        // YouTube specific metadata
        if (this.pageData?.type?.subtype === 'youtube') {
            const videoId = this.extractYouTubeVideoId();
            if (videoId) {
                metadata.videoId = videoId;
                metadata.embedUrl = `https://www.youtube.com/embed/${videoId}`;
            }

            // Try to get video duration, views, etc.
            const duration = document.querySelector('[class*="duration"]');
            if (duration) {
                metadata.duration = duration.textContent.trim();
            }
        }

        // Extract any schema.org structured data
        const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
        jsonLdScripts.forEach(script => {
            try {
                const data = JSON.parse(script.textContent);
                if (data['@type']) {
                    metadata.schemaType = data['@type'];
                }
            } catch (e) {
                // Ignore invalid JSON
            }
        });

        return metadata;
    }

    extractYouTubeVideoId() {
        const url = window.location.href;
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(regex);
        return match ? match[1] : null;
    }

    getPageData() {
        return this.pageData;
    }
}

// Initialize analyzer
const pageAnalyzer = new PageAnalyzer();

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getPageData') {
        sendResponse({
            success: true,
            data: pageAnalyzer.getPageData()
        });
    }
    return true;
});

// Re-analyze if page changes (for SPAs)
let lastUrl = window.location.href;
const observer = new MutationObserver(() => {
    if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        setTimeout(() => pageAnalyzer.analyzePage(), 1000);
    }
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});
