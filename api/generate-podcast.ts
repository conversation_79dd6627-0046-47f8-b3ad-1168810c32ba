import { NextRequest, NextResponse } from 'next/server';

// Types
interface SavedItem {
  id?: string;
  title: string;
  url: string;
  description: string;
  content?: {
    text?: string;
    excerpt?: string;
  };
  category?: {
    primary: string;
  };
  type?: {
    type: string;
    label: string;
  };
  author?: string;
  siteName?: string;
  tags?: string[];
  readingTime?: number;
  wordCount?: number;
}

interface PodcastRequest {
  savedItems: SavedItem[];
  hostA?: string;
  hostB?: string;
  targetLength?: number;
  focusArea?: string;
  style?: string;
}

interface VoiceConfig {
  voiceId: string;
  settings: {
    stability: number;
    similarity_boost: number;
    style: number;
    use_speaker_boost: boolean;
  };
}

// Voice configurations for each host
const VOICE_CONFIGS: Record<string, VoiceConfig> = {
  alex: {
    voiceId: 'pNInz6obpgDQGcFmaJgB', // Adam - thoughtful male
    settings: {
      stability: 0.75,
      similarity_boost: 0.85,
      style: 0.65,
      use_speaker_boost: true
    }
  },
  sam: {
    voiceId: 'EXAVITQu4vr4xnSDxMaL', // Bella - enthusiastic female
    settings: {
      stability: 0.65,
      similarity_boost: 0.80,
      style: 0.85,
      use_speaker_boost: true
    }
  },
  jordan: {
    voiceId: 'ErXwobaYiN019PkySvjV', // Antoni - practical male
    settings: {
      stability: 0.80,
      similarity_boost: 0.75,
      style: 0.55,
      use_speaker_boost: true
    }
  },
  riley: {
    voiceId: 'MF3mGyEYCl7XYWbV9V6O', // Elli - creative female
    settings: {
      stability: 0.70,
      similarity_boost: 0.82,
      style: 0.75,
      use_speaker_boost: true
    }
  }
};

// Rate limiting (simple in-memory store - use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 5; // 5 requests per minute

  const current = rateLimitStore.get(ip);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(ip, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}

async function generateScript(savedItems: SavedItem[], options: PodcastRequest): Promise<string> {
  const { hostA = 'alex', hostB = 'sam', targetLength = 10, focusArea = 'general', style = 'conversational' } = options;
  
  // Build content summary
  const contentSummary = savedItems.slice(0, 6).map(item => ({
    title: item.title,
    description: item.description,
    category: item.category?.primary || 'General',
    type: item.type?.label || 'Article',
    author: item.author,
    siteName: item.siteName,
    excerpt: item.content?.excerpt || item.description?.substring(0, 200)
  }));

  const prompt = `Create a natural, engaging podcast conversation between two AI hosts discussing saved web content. This should sound like NotebookLM quality - authentic, insightful, and genuinely conversational.

HOST PERSONALITIES:
${hostA}: Analytical and thoughtful - asks probing questions, connects ideas, thinks out loud
${hostB}: Enthusiastic and energetic - gets excited about concepts, makes connections, builds on ideas

CONTENT TO DISCUSS:
${contentSummary.map((item, i) => `
${i + 1}. "${item.title}" (${item.type})
   - Category: ${item.category}
   - Source: ${item.siteName || 'Unknown'}
   - Key insight: ${item.excerpt}
`).join('')}

CONVERSATION REQUIREMENTS:
- Target duration: ${targetLength} minutes (approximately ${Math.floor(targetLength * 150)} words)
- Style: ${style} - natural, authentic, engaging
- Include realistic interruptions, overlaps, and natural speech patterns
- Show genuine curiosity, surprise, and thoughtful reactions
- Reference specific details and insights from the content
- Build on each other's ideas organically
- Include natural "thinking out loud" moments

CONVERSATION STRUCTURE:
1. Engaging opening (30-45 seconds)
   - Natural greeting and rapport
   - Preview of content ahead
   - Set conversational tone

2. Main discussion (${targetLength - 1.5} minutes)
   - Explore each piece of content naturally
   - Make connections between different articles
   - Share insights and reactions
   - Build momentum through conversation

3. Thoughtful closing (30-45 seconds)
   - Synthesize key insights
   - Reflect on broader implications
   - Forward-looking statements

DIALOGUE FORMATTING:
- [${hostA.toUpperCase()}]: dialogue text
- [${hostB.toUpperCase()}]: dialogue text
- Keep stage directions minimal
- Focus on natural, conversational language
- No music cues or sound effects

Generate a complete, natural conversation that feels authentic and informative, matching NotebookLM's quality.`;

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
    },
    body: JSON.stringify({
      model: 'gpt-4-turbo-preview',
      messages: [
        {
          role: 'system',
          content: 'You are an expert podcast script writer specializing in creating natural, engaging conversations between AI hosts. Your scripts should sound authentic, insightful, and genuinely conversational.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 4000,
      temperature: 0.8,
      top_p: 0.9
    })
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI API error: ${response.status} - ${error}`);
  }

  const data = await response.json();
  return data.choices[0].message.content;
}

function parseScript(script: string, hostA: string, hostB: string): Array<{ host: string; text: string; voiceConfig: VoiceConfig }> {
  const lines = script.split('\n').filter(line => line.trim());
  const segments: Array<{ host: string; text: string; voiceConfig: VoiceConfig }> = [];
  
  for (const line of lines) {
    const hostAPattern = new RegExp(`\\[${hostA.toUpperCase()}\\]:\\s*(.+)`, 'i');
    const hostBPattern = new RegExp(`\\[${hostB.toUpperCase()}\\]:\\s*(.+)`, 'i');
    
    let match = line.match(hostAPattern);
    if (match) {
      const text = match[1].replace(/\[.*?\]/g, '').trim();
      if (text.length > 0) {
        segments.push({
          host: hostA,
          text,
          voiceConfig: VOICE_CONFIGS[hostA] || VOICE_CONFIGS.alex
        });
      }
      continue;
    }
    
    match = line.match(hostBPattern);
    if (match) {
      const text = match[1].replace(/\[.*?\]/g, '').trim();
      if (text.length > 0) {
        segments.push({
          host: hostB,
          text,
          voiceConfig: VOICE_CONFIGS[hostB] || VOICE_CONFIGS.sam
        });
      }
    }
  }
  
  return segments;
}

async function synthesizeVoice(text: string, voiceConfig: VoiceConfig): Promise<Buffer> {
  const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceConfig.voiceId}`, {
    method: 'POST',
    headers: {
      'Accept': 'audio/mpeg',
      'Content-Type': 'application/json',
      'xi-api-key': process.env.ELEVENLABS_API_KEY!
    },
    body: JSON.stringify({
      text,
      model_id: 'eleven_multilingual_v2',
      voice_settings: voiceConfig.settings
    })
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`ElevenLabs API error: ${response.status} - ${error}`);
  }

  return Buffer.from(await response.arrayBuffer());
}

function concatenateAudioBuffers(buffers: Buffer[]): Buffer {
  // Simple concatenation - in production, you might want proper audio mixing
  return Buffer.concat(buffers);
}

export async function POST(request: NextRequest) {
  try {
    // CORS headers
    const origin = request.headers.get('origin');
    const isValidOrigin = origin?.startsWith('chrome-extension://') || origin?.startsWith('moz-extension://');
    
    // Rate limiting
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    if (!checkRateLimit(ip)) {
      return new NextResponse('Rate limit exceeded', { 
        status: 429,
        headers: {
          'Access-Control-Allow-Origin': isValidOrigin ? origin! : '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      });
    }

    // Validate environment variables
    if (!process.env.OPENAI_API_KEY || !process.env.ELEVENLABS_API_KEY) {
      return new NextResponse('Server configuration error', { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': isValidOrigin ? origin! : '*',
        }
      });
    }

    // Parse request
    const body: PodcastRequest = await request.json();
    
    if (!body.savedItems || body.savedItems.length === 0) {
      return new NextResponse('No saved items provided', { 
        status: 400,
        headers: {
          'Access-Control-Allow-Origin': isValidOrigin ? origin! : '*',
        }
      });
    }

    // Generate script
    console.log('Generating script...');
    const script = await generateScript(body.savedItems, body);
    
    // Parse script into segments
    console.log('Parsing script...');
    const segments = parseScript(script, body.hostA || 'alex', body.hostB || 'sam');
    
    if (segments.length === 0) {
      throw new Error('No valid dialogue segments found in script');
    }

    // Generate audio for each segment
    console.log(`Generating audio for ${segments.length} segments...`);
    const audioBuffers: Buffer[] = [];
    
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i];
      console.log(`Processing segment ${i + 1}/${segments.length} (${segment.host})`);
      
      try {
        const audioBuffer = await synthesizeVoice(segment.text, segment.voiceConfig);
        audioBuffers.push(audioBuffer);
        
        // Add small pause between speakers (0.5 seconds of silence)
        if (i < segments.length - 1) {
          const pauseBuffer = Buffer.alloc(22050); // ~0.5 seconds at 44.1kHz
          audioBuffers.push(pauseBuffer);
        }
      } catch (error) {
        console.error(`Error synthesizing segment ${i + 1}:`, error);
        // Continue with other segments rather than failing completely
      }
    }

    if (audioBuffers.length === 0) {
      throw new Error('Failed to generate any audio segments');
    }

    // Concatenate audio
    console.log('Concatenating audio...');
    const finalAudio = concatenateAudioBuffers(audioBuffers);

    // Return audio response
    return new NextResponse(finalAudio, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Access-Control-Allow-Origin': isValidOrigin ? origin! : '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Content-Length': finalAudio.length.toString(),
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Podcast generation error:', error);
    
    const origin = request.headers.get('origin');
    const isValidOrigin = origin?.startsWith('chrome-extension://') || origin?.startsWith('moz-extension://');
    
    return new NextResponse(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`, {
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': isValidOrigin ? origin! : '*',
      }
    });
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin');
  const isValidOrigin = origin?.startsWith('chrome-extension://') || origin?.startsWith('moz-extension://');
  
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': isValidOrigin ? origin! : '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    }
  });
}
