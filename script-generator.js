// Conversational AI Script Generation Engine
class ScriptGenerator {
    constructor() {
        this.hostPersonalities = {
            alex: {
                name: "<PERSON>",
                traits: ["curious", "analytical", "detail-oriented"],
                voice: "thoughtful and measured",
                tendencies: ["asks probing questions", "connects ideas", "thinks out loud"],
                catchphrases: ["That's fascinating", "Let me think about this", "What strikes me is"],
                interruption_style: "polite and thoughtful"
            },
            sam: {
                name: "<PERSON>",
                traits: ["enthusiastic", "energetic", "optimistic"],
                voice: "animated and engaging",
                tendencies: ["gets excited about ideas", "makes connections to broader themes", "uses analogies"],
                catchphrases: ["Oh wow", "This is incredible", "You know what this reminds me of"],
                interruption_style: "excited and spontaneous"
            },
            jordan: {
                name: "<PERSON>",
                traits: ["practical", "skeptical", "grounded"],
                voice: "conversational and relatable",
                tendencies: ["asks practical questions", "challenges assumptions", "brings real-world perspective"],
                catchphrases: ["But wait", "Here's what I'm wondering", "In the real world"],
                interruption_style: "direct but friendly"
            },
            riley: {
                name: "<PERSON>",
                traits: ["creative", "imaginative", "big-picture"],
                voice: "expressive and dynamic",
                tendencies: ["sees patterns", "makes unexpected connections", "thinks about implications"],
                catchphrases: ["What if", "Imagine this", "The bigger picture here"],
                interruption_style: "builds on ideas enthusiastically"
            }
        };

        this.conversationPatterns = {
            opening: [
                "natural greeting and content preview",
                "establish rapport between hosts",
                "set expectations for the discussion"
            ],
            transitions: [
                "bridge between topics naturally",
                "reference previous points",
                "build momentum"
            ],
            reactions: [
                "genuine surprise or interest",
                "thoughtful pauses",
                "building on each other's ideas"
            ],
            closing: [
                "summarize key insights",
                "reflect on implications",
                "forward-looking statements"
            ]
        };
    }

    async generateScript(savedItems, options = {}) {
        const {
            hostA = 'alex',
            hostB = 'sam',
            targetLength = 10, // minutes
            focusArea = 'general',
            style = 'conversational',
            includePersonalReflections = true
        } = options;

        try {
            // Prepare content for script generation
            const contentSummary = this.prepareContentSummary(savedItems, focusArea);
            
            // Generate the conversational script
            const script = await this.generateConversationalScript(
                contentSummary,
                this.hostPersonalities[hostA],
                this.hostPersonalities[hostB],
                targetLength,
                style,
                includePersonalReflections
            );

            return {
                script,
                metadata: {
                    hostA: this.hostPersonalities[hostA],
                    hostB: this.hostPersonalities[hostB],
                    estimatedLength: targetLength,
                    contentItems: savedItems.length,
                    focusArea,
                    generatedAt: new Date().toISOString()
                }
            };
        } catch (error) {
            console.error('Error generating script:', error);
            throw new Error(`Script generation failed: ${error.message}`);
        }
    }

    prepareContentSummary(savedItems, focusArea) {
        // Filter and organize content based on focus area
        let relevantItems = savedItems;
        
        if (focusArea !== 'general') {
            relevantItems = savedItems.filter(item => 
                item.category?.primary?.toLowerCase().includes(focusArea.toLowerCase()) ||
                item.type?.type === focusArea ||
                item.tags?.some(tag => tag.toLowerCase().includes(focusArea.toLowerCase()))
            );
        }

        // Sort by relevance and recency
        relevantItems.sort((a, b) => {
            const aScore = this.calculateRelevanceScore(a, focusArea);
            const bScore = this.calculateRelevanceScore(b, focusArea);
            if (aScore !== bScore) return bScore - aScore;
            return b.timestamp - a.timestamp;
        });

        // Limit to most relevant items (max 8 for a good conversation)
        const selectedItems = relevantItems.slice(0, 8);

        return {
            items: selectedItems.map(item => ({
                title: item.title,
                url: item.url,
                description: item.description,
                content: item.content?.excerpt || item.description,
                category: item.category?.primary || 'General',
                readingTime: item.readingTime,
                author: item.author,
                siteName: item.siteName,
                tags: item.tags?.slice(0, 5) || [],
                type: item.type?.label || 'Article',
                keyPoints: this.extractKeyPoints(item)
            })),
            themes: this.identifyCommonThemes(selectedItems),
            totalReadingTime: selectedItems.reduce((sum, item) => sum + (item.readingTime || 0), 0),
            focusArea
        };
    }

    calculateRelevanceScore(item, focusArea) {
        let score = 0;
        
        // Category match
        if (item.category?.primary?.toLowerCase().includes(focusArea.toLowerCase())) {
            score += 3;
        }
        
        // Type match
        if (item.type?.type === focusArea) {
            score += 2;
        }
        
        // Tag matches
        const tagMatches = item.tags?.filter(tag => 
            tag.toLowerCase().includes(focusArea.toLowerCase())
        ).length || 0;
        score += tagMatches;
        
        // Content quality indicators
        if (item.readingTime > 3) score += 1; // Substantial content
        if (item.content?.text?.length > 1000) score += 1; // Rich content
        if (item.author) score += 0.5; // Authored content
        
        return score;
    }

    extractKeyPoints(item) {
        const content = item.content?.text || item.description || '';
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
        
        // Simple key point extraction (first few meaningful sentences)
        return sentences.slice(0, 3).map(s => s.trim()).filter(s => s.length > 0);
    }

    identifyCommonThemes(items) {
        const themes = {};
        
        items.forEach(item => {
            // Count categories
            const category = item.category?.primary || 'General';
            themes[category] = (themes[category] || 0) + 1;
            
            // Count common tags
            item.tags?.forEach(tag => {
                themes[tag] = (themes[tag] || 0) + 1;
            });
        });

        // Return top themes
        return Object.entries(themes)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([theme, count]) => ({ theme, count }));
    }

    async generateConversationalScript(contentSummary, hostA, hostB, targetLength, style, includePersonalReflections) {
        const prompt = this.buildScriptPrompt(contentSummary, hostA, hostB, targetLength, style, includePersonalReflections);
        
        // This would integrate with OpenAI API
        // For now, return a structured template
        return this.generateScriptTemplate(contentSummary, hostA, hostB, targetLength);
    }

    buildScriptPrompt(contentSummary, hostA, hostB, targetLength, style, includePersonalReflections) {
        return `You are creating a conversational podcast script between two AI hosts discussing saved web content. 

HOST PERSONALITIES:
${hostA.name}: ${hostA.traits.join(', ')} - ${hostA.voice}
- Tendencies: ${hostA.tendencies.join(', ')}
- Common phrases: ${hostA.catchphrases.join(', ')}
- Interruption style: ${hostA.interruption_style}

${hostB.name}: ${hostB.traits.join(', ')} - ${hostB.voice}
- Tendencies: ${hostB.tendencies.join(', ')}
- Common phrases: ${hostB.catchphrases.join(', ')}
- Interruption style: ${hostB.interruption_style}

CONTENT TO DISCUSS:
${contentSummary.items.map(item => `
- "${item.title}" (${item.type})
  Category: ${item.category}
  Key points: ${item.keyPoints.join('; ')}
  Reading time: ${item.readingTime} minutes
`).join('')}

COMMON THEMES: ${contentSummary.themes.map(t => t.theme).join(', ')}

REQUIREMENTS:
- Target length: ${targetLength} minutes (approximately ${targetLength * 150} words)
- Style: Natural, conversational, engaging
- Include realistic interruptions and overlaps
- Show genuine curiosity and reactions
- Reference specific details from the content
- Build on each other's ideas naturally
- Include "thinking out loud" moments
- Add natural pauses and emphasis cues
${includePersonalReflections ? '- Include personal AI reflections (clearly marked as AI-generated)' : ''}

SCRIPT STRUCTURE:
1. Engaging opening (30 seconds)
2. Content discussion with natural transitions (${targetLength - 1} minutes)
3. Closing with insights and forward-looking statements (30 seconds)

Format as a script with:
- [HOST NAME]: dialogue
- [PAUSE] for natural breaks
- [OVERLAPPING] for interruptions
- [EMPHASIS] for stressed words
- [THINKING] for contemplative moments

Generate a natural, engaging conversation that feels authentic and informative.`;
    }

    generateScriptTemplate(contentSummary, hostA, hostB, targetLength) {
        // This is a template - in production, this would be generated by GPT-4
        const items = contentSummary.items;
        const themes = contentSummary.themes;
        
        return `[OPENING MUSIC FADE IN]

[${hostA.name.toUpperCase()}]: Hey everyone, welcome back! I'm ${hostA.name}, and I'm here with ${hostB.name}. Today we're diving into some fascinating content that's been saved and we're honestly pretty excited about what we found.

[${hostB.name.toUpperCase()}]: [ENTHUSIASTIC] Oh absolutely! We've got ${items.length} pieces covering everything from ${themes.map(t => t.theme).slice(0, 3).join(', ')}. And ${hostA.name}, I have to say, there are some real gems in here.

[${hostA.name.toUpperCase()}]: [THINKING] You know what caught my attention right away? This piece about "${items[0]?.title}". ${items[0]?.keyPoints[0] || 'The insights here are really compelling.'} 

[${hostB.name.toUpperCase()}]: [INTERRUPTING] Wait, wait - that's the one from ${items[0]?.siteName || 'that site'}, right? [PAUSE] What strikes me about this is how it connects to something we were just discussing about ${themes[0]?.theme || 'technology trends'}.

[${hostA.name.toUpperCase()}]: [AGREEMENT] Exactly! And here's what's fascinating - [EMPHASIS] the author makes this point about ${items[0]?.keyPoints[1] || 'the broader implications'}, which honestly made me think about it in a completely different way.

[${hostB.name.toUpperCase()}]: [EXCITED] Oh wow, that reminds me of another piece we have here - "${items[1]?.title}". The connection between these two is incredible because...

[PAUSE]

[${hostA.name.toUpperCase()}]: [THOUGHTFUL] You know, as AI hosts, we should mention that while we're genuinely processing and reacting to this content, our perspectives are AI-generated. But that said, the patterns we're seeing across these ${items.length} articles are really compelling.

[${hostB.name.toUpperCase()}]: [BUILDING ON IDEA] Absolutely. And speaking of patterns, let's talk about this ${themes[1]?.theme || 'emerging theme'} that keeps coming up. In "${items[2]?.title || 'this next piece'}", there's this insight that...

[CONTINUE WITH NATURAL CONVERSATION FLOW]

[CLOSING]

[${hostA.name.toUpperCase()}]: [REFLECTIVE] So as we wrap up, what's your biggest takeaway from all of this, ${hostB.name}?

[${hostB.name.toUpperCase()}]: [THOUGHTFUL] I think it's how these seemingly different pieces - from ${themes.map(t => t.theme).slice(0, 2).join(' to ')} - all point to this larger shift in how we think about ${themes[0]?.theme || 'these topics'}. What about you?

[${hostA.name.toUpperCase()}]: [PAUSE] For me, it's the reminder that staying curious and connecting ideas across different domains is more important than ever. These ${contentSummary.totalReadingTime} minutes of reading time represent hours of human insight and research.

[${hostB.name.toUpperCase()}]: [FORWARD-LOOKING] And that's what makes this so exciting - we're not just consuming content, we're part of this ongoing conversation about where we're headed.

[${hostA.name.toUpperCase()}]: Thanks for joining us today. Keep saving those interesting articles, and we'll see you next time!

[CLOSING MUSIC FADE IN]

[END]`;
    }
}

// Make ScriptGenerator available globally
window.ScriptGenerator = ScriptGenerator;
