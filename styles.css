/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    width: 380px;
    max-height: 600px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    text-align: center;
}

.title {
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.icon {
    font-size: 20px;
}

/* Current page section */
.current-page {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.page-info {
    margin-bottom: 16px;
}

.page-type {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.type-icon {
    font-size: 14px;
}

.page-title {
    font-weight: 600;
    font-size: 15px;
    line-height: 1.3;
    margin-bottom: 6px;
    color: #212529;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.page-url {
    font-size: 12px;
    color: #6c757d;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.save-btn {
    width: 100%;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.save-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.save-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-icon {
    font-size: 16px;
}

/* Search section */
.search-section {
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.search-container {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.search-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
}

.search-btn:hover {
    background: #5a6fd8;
}

.filters-container {
    display: flex;
    gap: 8px;
}

.filter-select {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
}

/* Saved section */
.saved-section {
    padding: 20px;
    max-height: 350px;
    overflow-y: auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h2 {
    font-size: 16px;
    font-weight: 600;
    color: #212529;
}

.actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.count {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.podcast-btn,
.stats-btn,
.clear-btn {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.podcast-btn:hover,
.stats-btn:hover,
.clear-btn:hover {
    background: #f8f9fa;
}

.podcast-btn {
    color: #e91e63;
}

.podcast-btn:hover {
    background: #fce4ec;
}

/* Saved list */
.saved-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.saved-item {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    cursor: pointer;
}

.saved-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 6px;
}

.item-type {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
}

.delete-btn {
    background: none;
    border: none;
    font-size: 14px;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.delete-btn:hover {
    opacity: 1;
    background: #dc3545;
    color: white;
}

.item-title {
    font-weight: 600;
    font-size: 13px;
    line-height: 1.3;
    margin-bottom: 4px;
    color: #212529;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-url {
    font-size: 11px;
    color: #6c757d;
    word-break: break-all;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-date {
    font-size: 10px;
    color: #adb5bd;
    margin-top: 4px;
}

.item-meta {
    display: flex;
    gap: 8px;
    margin-top: 6px;
    flex-wrap: wrap;
}

.item-category,
.item-reading-time,
.item-word-count {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    background: #e9ecef;
    color: #6c757d;
}

.item-category {
    background: #e3f2fd;
    color: #1976d2;
}

.item-tags {
    display: flex;
    gap: 4px;
    margin-top: 4px;
    flex-wrap: wrap;
}

.item-tag {
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 6px;
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #e9ecef;
}

.item-preview-btn {
    background: none;
    border: none;
    font-size: 12px;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    opacity: 0.6;
    transition: all 0.2s ease;
    margin-left: 4px;
}

.item-preview-btn:hover {
    opacity: 1;
    background: #667eea;
    color: white;
}

.processing-indicator {
    font-size: 10px;
    color: #ffc107;
    margin-top: 2px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.processing-indicator.complete {
    color: #28a745;
}

.processing-indicator.error {
    color: #dc3545;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 12px;
}

.empty-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.empty-subtext {
    font-size: 12px;
    opacity: 0.8;
}

/* Status message */
.status-message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #212529;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.status-message.show {
    opacity: 1;
}

.status-message.success {
    background: #28a745;
}

.status-message.error {
    background: #dc3545;
}

.status-message.info {
    background: #17a2b8;
}

/* Scrollbar styling */
.saved-section::-webkit-scrollbar {
    width: 6px;
}

.saved-section::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.saved-section::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.saved-section::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Modal styles */
.preview-modal,
.stats-modal,
.podcast-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.preview-content,
.stats-content,
.podcast-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.podcast-content {
    max-width: 600px;
    max-height: 90%;
}

.preview-header,
.stats-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-header h3,
.stats-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.preview-body,
.stats-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.preview-meta {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 12px;
    line-height: 1.4;
}

.preview-text {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

.category-breakdown,
.type-breakdown {
    margin-bottom: 16px;
}

.breakdown-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f1f1f1;
}

.breakdown-item:last-child {
    border-bottom: none;
}

.breakdown-label {
    font-size: 13px;
    color: #333;
}

.breakdown-count {
    font-size: 12px;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 10px;
}

/* Podcast Modal Styles */
.podcast-header {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.podcast-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.podcast-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.podcast-settings {
    margin-bottom: 20px;
}

.setting-group {
    margin-bottom: 16px;
}

.setting-group label {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: #333;
    margin-bottom: 6px;
}

.host-selection {
    display: flex;
    align-items: center;
    gap: 8px;
}

.host-select,
.setting-select {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 13px;
    background: white;
}

.host-separator {
    font-size: 14px;
    font-weight: bold;
    color: #6c757d;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #333;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.api-keys-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.api-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 8px;
}

.api-help {
    margin-top: 8px;
}

.api-help small {
    color: #6c757d;
    font-size: 11px;
}

.api-help a {
    color: #e91e63;
    text-decoration: none;
}

.api-help a:hover {
    text-decoration: underline;
}

.podcast-actions {
    text-align: center;
    margin-bottom: 20px;
}

.generate-btn {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.generate-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
}

.generate-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.podcast-progress {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 13px;
    color: #6c757d;
}

.podcast-result {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.result-header h4 {
    margin: 0 0 16px 0;
    color: #28a745;
    font-size: 14px;
}

.audio-player {
    margin-bottom: 16px;
}

.result-actions {
    display: flex;
    gap: 8px;
}

.download-btn,
.script-btn {
    flex: 1;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: background-color 0.2s ease;
}

.download-btn:hover,
.script-btn:hover {
    background: #5a6fd8;
}
