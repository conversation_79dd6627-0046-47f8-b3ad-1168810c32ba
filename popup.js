// Popup script for Chrome extension
class PopupManager {
    constructor() {
        this.currentPageData = null;
        this.savedItems = [];
        this.init();
    }

    async init() {
        await this.loadSavedItems();
        await this.loadCurrentPageData();
        this.setupEventListeners();
        this.updateUI();
    }

    async loadCurrentPageData() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('Cannot save this type of page');
                return;
            }

            // Inject content script if needed
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content.js']
                });
            } catch (e) {
                // Content script might already be injected
            }

            // Get page data from content script
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'getPageData' });
            
            if (response && response.success) {
                this.currentPageData = response.data;
                this.updateCurrentPageUI();
            } else {
                // Fallback to basic tab data
                this.currentPageData = {
                    url: tab.url,
                    title: tab.title || 'Untitled Page',
                    type: {
                        type: 'webpage',
                        subtype: 'general',
                        icon: '🌐',
                        label: 'Web Page'
                    },
                    description: '',
                    author: '',
                    publishDate: '',
                    thumbnail: '',
                    metadata: {},
                    timestamp: Date.now()
                };
                this.updateCurrentPageUI();
            }
        } catch (error) {
            console.error('Error loading page data:', error);
            this.showError('Failed to load page information');
        }
    }

    async loadSavedItems() {
        try {
            const result = await chrome.storage.local.get(['savedPages']);
            this.savedItems = result.savedPages || [];
            this.updateSavedItemsUI();
        } catch (error) {
            console.error('Error loading saved items:', error);
            this.showError('Failed to load saved items');
        }
    }

    setupEventListeners() {
        // Save button
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveCurrentPage();
        });

        // Clear all button
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearAllItems();
        });
    }

    updateCurrentPageUI() {
        if (!this.currentPageData) return;

        const typeIcon = document.getElementById('typeIcon');
        const typeText = document.getElementById('typeText');
        const pageTitle = document.getElementById('pageTitle');
        const pageUrl = document.getElementById('pageUrl');
        const saveBtn = document.getElementById('saveBtn');

        typeIcon.textContent = this.currentPageData.type.icon;
        typeText.textContent = this.currentPageData.type.label;
        pageTitle.textContent = this.currentPageData.title;
        pageUrl.textContent = this.currentPageData.url;

        // Enable save button
        saveBtn.disabled = false;
    }

    updateSavedItemsUI() {
        const savedList = document.getElementById('savedList');
        const emptyState = document.getElementById('emptyState');
        const savedCount = document.getElementById('savedCount');

        // Update count
        savedCount.textContent = `${this.savedItems.length} item${this.savedItems.length !== 1 ? 's' : ''}`;

        if (this.savedItems.length === 0) {
            emptyState.style.display = 'block';
            savedList.innerHTML = '';
            savedList.appendChild(emptyState);
            return;
        }

        emptyState.style.display = 'none';
        savedList.innerHTML = '';

        // Sort by timestamp (newest first)
        const sortedItems = [...this.savedItems].sort((a, b) => b.timestamp - a.timestamp);

        sortedItems.forEach((item, index) => {
            const itemElement = this.createSavedItemElement(item, index);
            savedList.appendChild(itemElement);
        });
    }

    createSavedItemElement(item, index) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'saved-item';
        
        const date = new Date(item.timestamp).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        itemDiv.innerHTML = `
            <div class="item-header">
                <div class="item-type">
                    <span>${item.type.icon}</span>
                    <span>${item.type.label}</span>
                </div>
                <button class="delete-btn" data-index="${index}" title="Delete">🗑️</button>
            </div>
            <div class="item-title">${item.title}</div>
            <div class="item-url">${item.url}</div>
            <div class="item-date">${date}</div>
        `;

        // Add click handler to open URL
        itemDiv.addEventListener('click', (e) => {
            if (!e.target.classList.contains('delete-btn')) {
                chrome.tabs.create({ url: item.url });
            }
        });

        // Add delete handler
        const deleteBtn = itemDiv.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteItem(index);
        });

        return itemDiv;
    }

    async saveCurrentPage() {
        if (!this.currentPageData) {
            this.showError('No page data available');
            return;
        }

        try {
            // Check if already saved
            const existingIndex = this.savedItems.findIndex(item => item.url === this.currentPageData.url);
            
            if (existingIndex !== -1) {
                // Update existing item
                this.savedItems[existingIndex] = { ...this.currentPageData, timestamp: Date.now() };
                this.showSuccess('Page updated successfully');
            } else {
                // Add new item
                this.savedItems.unshift(this.currentPageData);
                this.showSuccess('Page saved successfully');
            }

            // Save to storage
            await chrome.storage.local.set({ savedPages: this.savedItems });
            this.updateSavedItemsUI();

        } catch (error) {
            console.error('Error saving page:', error);
            this.showError('Failed to save page');
        }
    }

    async deleteItem(index) {
        try {
            this.savedItems.splice(index, 1);
            await chrome.storage.local.set({ savedPages: this.savedItems });
            this.updateSavedItemsUI();
            this.showSuccess('Item deleted');
        } catch (error) {
            console.error('Error deleting item:', error);
            this.showError('Failed to delete item');
        }
    }

    async clearAllItems() {
        if (this.savedItems.length === 0) return;

        if (confirm('Are you sure you want to delete all saved pages?')) {
            try {
                this.savedItems = [];
                await chrome.storage.local.set({ savedPages: [] });
                this.updateSavedItemsUI();
                this.showSuccess('All items cleared');
            } catch (error) {
                console.error('Error clearing items:', error);
                this.showError('Failed to clear items');
            }
        }
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showMessage(message, type = '') {
        const statusMessage = document.getElementById('statusMessage');
        statusMessage.textContent = message;
        statusMessage.className = `status-message show ${type}`;
        
        setTimeout(() => {
            statusMessage.classList.remove('show');
        }, 3000);
    }

    updateUI() {
        this.updateCurrentPageUI();
        this.updateSavedItemsUI();
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
