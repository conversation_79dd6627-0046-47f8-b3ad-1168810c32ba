// Enhanced popup script for Chrome extension
class PopupManager {
    constructor() {
        this.currentPageData = null;
        this.savedItems = [];
        this.filteredItems = [];
        this.currentFilters = {};
        this.searchQuery = '';
        this.categories = { categories: [], tags: [], types: [] };
        this.init();
    }

    async init() {
        await this.loadCategories();
        await this.loadSavedItems();
        await this.loadCurrentPageData();
        this.setupEventListeners();
        this.updateUI();
    }

    async loadCurrentPageData() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('Cannot save this type of page');
                return;
            }

            // Always inject content script to ensure it's fresh
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content.js']
                });

                // Wait a bit for the script to initialize
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (e) {
                console.log('Content script injection failed:', e);
            }

            // Try to get page data from content script with retries
            let response = null;
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts && (!response || !response.success)) {
                try {
                    response = await chrome.tabs.sendMessage(tab.id, { action: 'getPageData' });
                    if (response && response.success) {
                        break;
                    }
                } catch (e) {
                    console.log(`Attempt ${attempts + 1} failed:`, e);
                }

                attempts++;
                if (attempts < maxAttempts) {
                    // Wait before retry, increasing delay each time
                    await new Promise(resolve => setTimeout(resolve, 200 * attempts));
                }
            }

            if (response && response.success) {
                this.currentPageData = response.data;
                this.updateCurrentPageUI();
            } else {
                // Fallback to basic tab data
                console.log('Using fallback data for tab:', tab.url);
                this.currentPageData = {
                    url: tab.url,
                    title: tab.title || 'Untitled Page',
                    type: {
                        type: 'webpage',
                        subtype: 'general',
                        icon: '🌐',
                        label: 'Web Page'
                    },
                    description: '',
                    author: '',
                    publishDate: '',
                    thumbnail: '',
                    metadata: {},
                    timestamp: Date.now()
                };
                this.updateCurrentPageUI();
            }
        } catch (error) {
            console.error('Error loading page data:', error);
            this.showError('Failed to load page information');
        }
    }

    async loadCategories() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getCategories' });
            if (response.success) {
                this.categories = response.data;
                this.updateFilterOptions();
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async loadSavedItems(filters = {}) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getSavedPages',
                filters: filters
            });

            if (response.success) {
                this.savedItems = response.data;
                this.filteredItems = [...this.savedItems];
                this.updateSavedItemsUI();
            }
        } catch (error) {
            console.error('Error loading saved items:', error);
            this.showError('Failed to load saved items');
        }
    }

    async searchSavedItems(query, filters = {}) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'searchPages',
                query: query,
                filters: filters
            });

            if (response.success) {
                this.filteredItems = response.data;
                this.updateSavedItemsUI();
            }
        } catch (error) {
            console.error('Error searching items:', error);
            this.showError('Failed to search items');
        }
    }

    setupEventListeners() {
        // Save button
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveCurrentPage();
        });

        // Clear all button
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearAllItems();
        });

        // Stats button
        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStats();
        });

        // Podcast button
        document.getElementById('podcastBtn').addEventListener('click', () => {
            this.showPodcastGenerator();
        });

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');

        searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.debounceSearch();
        });

        searchBtn.addEventListener('click', () => {
            this.performSearch();
        });

        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // Filter functionality
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.currentFilters.category = e.target.value;
            this.applyFilters();
        });

        document.getElementById('typeFilter').addEventListener('change', (e) => {
            this.currentFilters.type = e.target.value;
            this.applyFilters();
        });

        document.getElementById('sortFilter').addEventListener('change', (e) => {
            const [sortBy, sortOrder] = e.target.value.split('-');
            this.currentFilters.sortBy = sortBy;
            this.currentFilters.sortOrder = sortOrder || 'desc';
            this.applyFilters();
        });

        // Modal functionality
        document.getElementById('closePreview').addEventListener('click', () => {
            this.hidePreview();
        });

        document.getElementById('closeStats').addEventListener('click', () => {
            this.hideStats();
        });

        // Podcast modal functionality
        document.getElementById('closePodcast').addEventListener('click', () => {
            this.hidePodcastGenerator();
        });

        document.getElementById('generatePodcast').addEventListener('click', () => {
            this.generatePodcast();
        });

        // Close modals on background click
        document.getElementById('previewModal').addEventListener('click', (e) => {
            if (e.target.id === 'previewModal') {
                this.hidePreview();
            }
        });

        document.getElementById('statsModal').addEventListener('click', (e) => {
            if (e.target.id === 'statsModal') {
                this.hideStats();
            }
        });

        document.getElementById('podcastModal').addEventListener('click', (e) => {
            if (e.target.id === 'podcastModal') {
                this.hidePodcastGenerator();
            }
        });
    }

    debounceSearch() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch();
        }, 300);
    }

    async performSearch() {
        if (this.searchQuery.trim()) {
            await this.searchSavedItems(this.searchQuery, this.currentFilters);
        } else {
            await this.loadSavedItems(this.currentFilters);
        }
    }

    async applyFilters() {
        if (this.searchQuery.trim()) {
            await this.searchSavedItems(this.searchQuery, this.currentFilters);
        } else {
            await this.loadSavedItems(this.currentFilters);
        }
    }

    updateCurrentPageUI() {
        if (!this.currentPageData) return;

        const typeIcon = document.getElementById('typeIcon');
        const typeText = document.getElementById('typeText');
        const pageTitle = document.getElementById('pageTitle');
        const pageUrl = document.getElementById('pageUrl');
        const saveBtn = document.getElementById('saveBtn');

        typeIcon.textContent = this.currentPageData.type.icon;
        typeText.textContent = this.currentPageData.type.label;
        pageTitle.textContent = this.currentPageData.title;
        pageUrl.textContent = this.currentPageData.url;

        // Enable save button
        saveBtn.disabled = false;
    }

    updateFilterOptions() {
        const categoryFilter = document.getElementById('categoryFilter');
        const typeFilter = document.getElementById('typeFilter');

        // Update category filter
        categoryFilter.innerHTML = '<option value="">All Categories</option>';
        this.categories.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });

        // Update type filter
        typeFilter.innerHTML = '<option value="">All Types</option>';
        this.categories.types.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = type.charAt(0).toUpperCase() + type.slice(1);
            typeFilter.appendChild(option);
        });
    }

    updateSavedItemsUI() {
        const savedList = document.getElementById('savedList');
        const emptyState = document.getElementById('emptyState');
        const savedCount = document.getElementById('savedCount');

        // Update count
        const totalCount = this.savedItems.length;
        const filteredCount = this.filteredItems.length;

        if (filteredCount !== totalCount) {
            savedCount.textContent = `${filteredCount} of ${totalCount} items`;
        } else {
            savedCount.textContent = `${totalCount} item${totalCount !== 1 ? 's' : ''}`;
        }

        if (this.filteredItems.length === 0) {
            emptyState.style.display = 'block';
            savedList.innerHTML = '';
            savedList.appendChild(emptyState);

            if (this.searchQuery || Object.keys(this.currentFilters).length > 0) {
                emptyState.querySelector('.empty-text').textContent = 'No matching pages found';
                emptyState.querySelector('.empty-subtext').textContent = 'Try adjusting your search or filters';
            } else {
                emptyState.querySelector('.empty-text').textContent = 'No saved pages yet';
                emptyState.querySelector('.empty-subtext').textContent = 'Click "Save Page" to get started';
            }
            return;
        }

        emptyState.style.display = 'none';
        savedList.innerHTML = '';

        this.filteredItems.forEach((item, index) => {
            const itemElement = this.createSavedItemElement(item, index);
            savedList.appendChild(itemElement);
        });
    }

    createSavedItemElement(item, index) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'saved-item';

        const date = new Date(item.timestamp).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // Enhanced metadata display
        const category = item.category?.primary || 'General';
        const readingTime = item.readingTime ? `${item.readingTime} min read` : '';
        const wordCount = item.wordCount ? `${item.wordCount} words` : '';

        // Processing status
        let processingStatus = '';
        if (item.processingStatus === 'queued' || item.processingStatus === 'processing') {
            processingStatus = '<div class="processing-indicator">⏳ Processing...</div>';
        } else if (item.processingStatus === 'complete') {
            processingStatus = '<div class="processing-indicator complete">✅ Enhanced</div>';
        } else if (item.processingStatus === 'error') {
            processingStatus = '<div class="processing-indicator error">❌ Processing failed</div>';
        }

        // Tags display
        const tagsHtml = item.tags && item.tags.length > 0
            ? `<div class="item-tags">${item.tags.slice(0, 3).map(tag => `<span class="item-tag">${tag}</span>`).join('')}</div>`
            : '';

        itemDiv.innerHTML = `
            <div class="item-header">
                <div class="item-type">
                    <span>${item.type.icon}</span>
                    <span>${item.type.label}</span>
                </div>
                <div class="item-actions">
                    ${item.content?.text ? '<button class="item-preview-btn" title="Preview content">👁️</button>' : ''}
                    <button class="delete-btn" data-index="${index}" title="Delete">🗑️</button>
                </div>
            </div>
            <div class="item-title">${item.title}</div>
            <div class="item-url">${item.url}</div>
            <div class="item-meta">
                <span class="item-category">${category}</span>
                ${readingTime ? `<span class="item-reading-time">📖 ${readingTime}</span>` : ''}
                ${wordCount ? `<span class="item-word-count">📝 ${wordCount}</span>` : ''}
            </div>
            ${tagsHtml}
            <div class="item-date">${date}</div>
            ${processingStatus}
        `;

        // Add click handler to open URL
        itemDiv.addEventListener('click', (e) => {
            if (!e.target.classList.contains('delete-btn') && !e.target.classList.contains('item-preview-btn')) {
                chrome.tabs.create({ url: item.url });
            }
        });

        // Add delete handler
        const deleteBtn = itemDiv.querySelector('.delete-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteItem(this.savedItems.findIndex(savedItem => savedItem.id === item.id || savedItem.url === item.url));
            });
        }

        // Add preview handler
        const previewBtn = itemDiv.querySelector('.item-preview-btn');
        if (previewBtn) {
            previewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showPreview(item);
            });
        }

        return itemDiv;
    }

    async saveCurrentPage() {
        if (!this.currentPageData) {
            this.showError('No page data available');
            return;
        }

        try {
            // Check if already saved
            const existingIndex = this.savedItems.findIndex(item => item.url === this.currentPageData.url);
            
            if (existingIndex !== -1) {
                // Update existing item
                this.savedItems[existingIndex] = { ...this.currentPageData, timestamp: Date.now() };
                this.showSuccess('Page updated successfully');
            } else {
                // Add new item
                this.savedItems.unshift(this.currentPageData);
                this.showSuccess('Page saved successfully');
            }

            // Save to storage
            await chrome.storage.local.set({ savedPages: this.savedItems });
            this.updateSavedItemsUI();

        } catch (error) {
            console.error('Error saving page:', error);
            this.showError('Failed to save page');
        }
    }

    async deleteItem(index) {
        try {
            this.savedItems.splice(index, 1);
            await chrome.storage.local.set({ savedPages: this.savedItems });
            this.updateSavedItemsUI();
            this.showSuccess('Item deleted');
        } catch (error) {
            console.error('Error deleting item:', error);
            this.showError('Failed to delete item');
        }
    }

    async clearAllItems() {
        if (this.savedItems.length === 0) return;

        if (confirm('Are you sure you want to delete all saved pages?')) {
            try {
                this.savedItems = [];
                await chrome.storage.local.set({ savedPages: [] });
                this.updateSavedItemsUI();
                this.showSuccess('All items cleared');
            } catch (error) {
                console.error('Error clearing items:', error);
                this.showError('Failed to clear items');
            }
        }
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showInfo(message) {
        this.showMessage(message, 'info');
    }

    showMessage(message, type = '') {
        const statusMessage = document.getElementById('statusMessage');
        statusMessage.textContent = message;
        statusMessage.className = `status-message show ${type}`;
        
        setTimeout(() => {
            statusMessage.classList.remove('show');
        }, 3000);
    }

    showPreview(item) {
        const modal = document.getElementById('previewModal');
        const title = document.getElementById('previewTitle');
        const meta = document.getElementById('previewMeta');
        const text = document.getElementById('previewText');

        title.textContent = item.title;

        // Build metadata display
        const metaInfo = [];
        if (item.author) metaInfo.push(`Author: ${item.author}`);
        if (item.category?.primary) metaInfo.push(`Category: ${item.category.primary}`);
        if (item.readingTime) metaInfo.push(`Reading time: ${item.readingTime} minutes`);
        if (item.wordCount) metaInfo.push(`Word count: ${item.wordCount}`);
        if (item.publishDate) {
            const pubDate = new Date(item.publishDate).toLocaleDateString();
            metaInfo.push(`Published: ${pubDate}`);
        }

        meta.innerHTML = metaInfo.join(' • ');

        // Display content
        if (item.content?.text) {
            text.textContent = item.content.text.substring(0, 2000) + (item.content.text.length > 2000 ? '...' : '');
        } else if (item.content?.excerpt) {
            text.textContent = item.content.excerpt;
        } else {
            text.textContent = item.description || 'No content preview available.';
        }

        modal.style.display = 'flex';
    }

    hidePreview() {
        document.getElementById('previewModal').style.display = 'none';
    }

    async showStats() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getStats' });
            if (response.success) {
                const stats = response.data;
                const modal = document.getElementById('statsModal');
                const body = document.getElementById('statsBody');

                body.innerHTML = `
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value">${stats.totalSaved || 0}</span>
                            <span class="stat-label">Total Saved</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.totalReadingTime || 0}</span>
                            <span class="stat-label">Minutes to Read</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.totalWordCount || 0}</span>
                            <span class="stat-label">Total Words</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.averageReadingTime || 0}</span>
                            <span class="stat-label">Avg. Reading Time</span>
                        </div>
                    </div>

                    ${stats.categoryBreakdown ? this.createBreakdownHTML('Categories', stats.categoryBreakdown) : ''}
                    ${stats.typeBreakdown ? this.createBreakdownHTML('Content Types', stats.typeBreakdown) : ''}
                `;

                modal.style.display = 'flex';
            }
        } catch (error) {
            console.error('Error loading stats:', error);
            this.showError('Failed to load statistics');
        }
    }

    createBreakdownHTML(title, breakdown) {
        const items = Object.entries(breakdown)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([key, value]) => `
                <div class="breakdown-item">
                    <span class="breakdown-label">${key}</span>
                    <span class="breakdown-count">${value}</span>
                </div>
            `).join('');

        return `
            <div class="${title.toLowerCase().replace(' ', '-')}-breakdown">
                <div class="breakdown-title">${title}</div>
                ${items}
            </div>
        `;
    }

    hideStats() {
        document.getElementById('statsModal').style.display = 'none';
    }

    showPodcastGenerator() {
        if (this.savedItems.length === 0) {
            this.showError('No saved content available for podcast generation');
            return;
        }

        const modal = document.getElementById('podcastModal');

        // Reset form
        document.getElementById('hostA').value = 'sam';
        document.getElementById('hostB').value = 'alex';
        document.getElementById('podcastLength').value = '10';
        document.getElementById('podcastFocus').value = 'general';
        document.getElementById('podcastStyle').value = 'conversational';
        document.getElementById('includeMusic').checked = true;
        document.getElementById('includeReflections').checked = true;

        // Hide progress and result sections
        document.getElementById('podcastProgress').style.display = 'none';
        document.getElementById('podcastResult').style.display = 'none';

        modal.style.display = 'flex';
    }

    hidePodcastGenerator() {
        document.getElementById('podcastModal').style.display = 'none';
    }

    async generatePodcast() {
        // Configuration - UPDATE THIS URL after deploying to Vercel
        const API_BASE_URL = 'https://your-project-name.vercel.app'; // TODO: Replace with your actual Vercel URL

        const hostA = document.getElementById('hostA').value;
        const hostB = document.getElementById('hostB').value;
        const targetLength = parseInt(document.getElementById('podcastLength').value);
        const focusArea = document.getElementById('podcastFocus').value;
        const style = document.getElementById('podcastStyle').value;
        const includeMusic = document.getElementById('includeMusic').checked;
        const includeReflections = document.getElementById('includeReflections').checked;

        // Validate inputs
        if (hostA === hostB) {
            this.showError('Please select different personalities for each host');
            return;
        }

        if (this.savedItems.length === 0) {
            this.showError('No saved content available for podcast generation');
            return;
        }

        try {
            // Show progress
            this.showPodcastProgress();

            // Filter content based on focus area
            let contentToUse = this.savedItems;
            if (focusArea !== 'general') {
                contentToUse = this.savedItems.filter(item =>
                    item.category?.primary?.toLowerCase().includes(focusArea.toLowerCase()) ||
                    item.type?.type === focusArea
                );
            }

            if (contentToUse.length === 0) {
                this.showError(`No content found for focus area: ${focusArea}`);
                this.hidePodcastProgress();
                return;
            }

            // Call the Vercel API
            this.updatePodcastProgress({ detail: { progress: 25, message: 'Connecting to AI services...' } });

            const response = await fetch(`${API_BASE_URL}/api/generate-podcast`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    savedItems: contentToUse.slice(0, 6), // Limit to 6 items for performance
                    hostA,
                    hostB,
                    targetLength,
                    focusArea,
                    style
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`API Error: ${response.status} - ${errorText}`);
            }

            this.updatePodcastProgress({ detail: { progress: 90, message: 'Finalizing audio...' } });

            // Get audio blob
            const audioBlob = await response.blob();

            // Show result
            this.showPodcastResult({
                audioBlob,
                script: 'Generated via production API - view in browser console for details',
                metadata: {
                    hostA,
                    hostB,
                    targetLength,
                    contentItems: contentToUse.length,
                    focusArea,
                    style,
                    generatedAt: new Date().toISOString(),
                    apiGenerated: true
                }
            });

        } catch (error) {
            console.error('Error generating podcast:', error);

            // Fallback to demo mode if API fails
            if (error.message.includes('fetch')) {
                this.showInfo('API unavailable - generating demo podcast instead');
                await this.generateDemoPodcast(hostA, hostB, targetLength, focusArea, style);
            } else {
                this.showError(`Podcast generation failed: ${error.message}`);
                this.hidePodcastProgress();
            }
        }
    }

    async checkProxyServer() {
        try {
            const response = await fetch('http://localhost:3001/health');
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    async generateRealPodcast(contentToUse, options) {
        // Set API keys on server
        await fetch('http://localhost:3001/api/set-keys', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                openaiKey: options.openaiKey,
                elevenlabsKey: options.elevenlabsKey
            })
        });

        // Generate podcast
        const response = await fetch('http://localhost:3001/api/generate-podcast', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                savedItems: contentToUse,
                options: options
            })
        });

        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }

        const result = await response.json();

        // Convert base64 audio to blob
        const audioData = atob(result.audio);
        const audioArray = new Uint8Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
            audioArray[i] = audioData.charCodeAt(i);
        }
        const audioBlob = new Blob([audioArray], { type: 'audio/mpeg' });

        return {
            audioBlob: audioBlob,
            script: result.script,
            metadata: result.metadata
        };
    }

    async generateDemoPodcast(hostA, hostB, targetLength, focusArea, style) {
        this.showPodcastProgress();

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Generate demo script based on saved content
        const demoScript = this.generateDemoScript(hostA, hostB, targetLength, focusArea);

        // Create demo audio (text-to-speech using browser's built-in capabilities)
        const audioBlob = await this.generateDemoAudio(demoScript, hostA, hostB);

        const result = {
            audioBlob: audioBlob,
            script: demoScript,
            metadata: {
                hostA: hostA,
                hostB: hostB,
                estimatedLength: targetLength,
                contentItems: this.savedItems.length,
                focusArea: focusArea,
                style: style,
                demo: true,
                generatedAt: new Date().toISOString()
            }
        };

        this.showPodcastResult(result);
    }

    generateDemoScript(hostA, hostB, targetLength, focusArea) {
        const items = this.savedItems.slice(0, 3); // Use first 3 items for demo

        const hostAName = hostA.charAt(0).toUpperCase() + hostA.slice(1);
        const hostBName = hostB.charAt(0).toUpperCase() + hostB.slice(1);

        return `[DEMO PODCAST SCRIPT]

[${hostAName.toUpperCase()}]: Hey everyone, welcome to our AI-generated podcast! I'm ${hostAName}, and I'm here with ${hostBName}. Today we're discussing some fascinating content that's been saved.

[${hostBName.toUpperCase()}]: Thanks ${hostAName}! I'm really excited about this. We've got ${this.savedItems.length} pieces of content covering ${focusArea === 'general' ? 'various topics' : focusArea}, and there are some real gems here.

[${hostAName.toUpperCase()}]: Absolutely! Let's start with "${items[0]?.title || 'this first article'}". What caught my attention was ${items[0]?.description?.substring(0, 100) || 'the unique perspective it offers'}...

[${hostBName.toUpperCase()}]: [EXCITED] Oh wow, that's fascinating! And it connects beautifully to "${items[1]?.title || 'our second piece'}" which talks about ${items[1]?.description?.substring(0, 80) || 'related concepts'}...

[${hostAName.toUpperCase()}]: [THOUGHTFUL] You know, as AI hosts, we should mention that this is a demonstration of our podcast generation capabilities. In a real scenario, we'd have much richer content analysis and natural conversation flow.

[${hostBName.toUpperCase()}]: Exactly! And speaking of "${items[2]?.title || 'our third article'}", the insights there about ${items[2]?.category?.primary || 'the topic'} really tie everything together.

[${hostAName.toUpperCase()}]: [REFLECTIVE] So as we wrap up this demo, what's exciting is how this technology can transform your saved content into engaging audio discussions.

[${hostBName.toUpperCase()}]: [FORWARD-LOOKING] Absolutely! With proper API keys, this system can generate much longer, more detailed conversations using GPT-4 and professional voice synthesis.

[${hostAName.toUpperCase()}]: Thanks for listening to this demo! To generate real podcasts, add your OpenAI and ElevenLabs API keys.

[END DEMO]`;
    }

    async generateDemoAudio(script, hostA, hostB) {
        // Use browser's built-in speech synthesis for demo
        if (!('speechSynthesis' in window)) {
            // Fallback: create a silent audio file
            return this.createSilentAudio(30000); // 30 seconds of silence
        }

        try {
            const audioChunks = [];
            const lines = script.split('\n').filter(line => line.includes(':'));

            for (const line of lines.slice(0, 6)) { // Limit to first 6 lines for demo
                const text = line.split(':')[1]?.replace(/\[.*?\]/g, '').trim();
                if (text && text.length > 0) {
                    const audioChunk = await this.textToSpeech(text);
                    audioChunks.push(audioChunk);
                }
            }

            // Combine audio chunks (simplified)
            if (audioChunks.length > 0) {
                return audioChunks[0]; // Return first chunk for demo
            }

            return this.createSilentAudio(30000);

        } catch (error) {
            console.error('Error generating demo audio:', error);
            return this.createSilentAudio(30000);
        }
    }

    async textToSpeech(text) {
        return new Promise((resolve) => {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.9;
            utterance.pitch = 1.0;
            utterance.volume = 1.0;

            // Try to find a good voice
            const voices = speechSynthesis.getVoices();
            const preferredVoice = voices.find(voice =>
                voice.lang.startsWith('en') && voice.name.includes('Google')
            ) || voices.find(voice => voice.lang.startsWith('en')) || voices[0];

            if (preferredVoice) {
                utterance.voice = preferredVoice;
            }

            utterance.onend = () => {
                // Create a simple audio blob (this is a simplified approach)
                resolve(this.createSilentAudio(5000)); // 5 seconds per segment
            };

            utterance.onerror = () => {
                resolve(this.createSilentAudio(5000));
            };

            speechSynthesis.speak(utterance);
        });
    }

    createSilentAudio(durationMs) {
        const sampleRate = 44100;
        const samples = Math.ceil(durationMs * sampleRate / 1000);
        const buffer = new ArrayBuffer(44 + samples * 2);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, 36 + samples * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, samples * 2, true);

        // Silent audio data (all zeros)
        for (let i = 44; i < buffer.byteLength; i += 2) {
            view.setInt16(i, 0, true);
        }

        return new Blob([buffer], { type: 'audio/wav' });
    }

    async loadPodcastScripts() {
        const scripts = [
            'script-generator.js',
            'voice-synthesizer.js',
            'podcast-generator.js'
        ];

        for (const script of scripts) {
            await this.loadScript(script);
        }
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    showPodcastProgress() {
        document.getElementById('podcastProgress').style.display = 'block';
        document.getElementById('podcastResult').style.display = 'none';

        // Listen for progress updates
        window.addEventListener('podcastProgress', this.updatePodcastProgress.bind(this));

        // Simulate progress for demo
        this.simulateProgress();
    }

    updatePodcastProgress(event) {
        const { progress, message } = event.detail;
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        progressFill.style.width = `${progress}%`;
        progressText.textContent = message;
    }

    simulateProgress() {
        const stages = [
            { progress: 10, message: 'Analyzing saved content...' },
            { progress: 25, message: 'Generating conversational script...' },
            { progress: 50, message: 'Processing with GPT-4...' },
            { progress: 70, message: 'Synthesizing voices...' },
            { progress: 85, message: 'Adding background music...' },
            { progress: 95, message: 'Finalizing audio...' },
            { progress: 100, message: 'Complete!' }
        ];

        let currentStage = 0;
        const interval = setInterval(() => {
            if (currentStage < stages.length) {
                this.updatePodcastProgress({ detail: stages[currentStage] });
                currentStage++;
            } else {
                clearInterval(interval);
            }
        }, 2000);
    }

    hidePodcastProgress() {
        document.getElementById('podcastProgress').style.display = 'none';
        window.removeEventListener('podcastProgress', this.updatePodcastProgress.bind(this));
    }

    showPodcastResult(result) {
        this.hidePodcastProgress();

        const resultDiv = document.getElementById('podcastResult');
        const audioPlayer = document.getElementById('podcastAudio');

        // Set audio source
        const audioUrl = URL.createObjectURL(result.audioBlob);
        audioPlayer.src = audioUrl;

        // Store result for download
        this.currentPodcastResult = result;

        // Show result section
        resultDiv.style.display = 'block';

        // Setup download handler
        document.getElementById('downloadPodcast').onclick = () => {
            this.downloadPodcast(result);
        };

        // Setup script view handler
        document.getElementById('viewScript').onclick = () => {
            this.viewPodcastScript(result.script);
        };
    }

    downloadPodcast(result) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `ai-podcast-${timestamp}.mp3`;

        const url = URL.createObjectURL(result.audioBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();

        setTimeout(() => URL.revokeObjectURL(url), 1000);
        this.showSuccess('Podcast downloaded successfully');
    }

    viewPodcastScript(script) {
        // Open script in a new window or show in modal
        const scriptWindow = window.open('', '_blank');
        scriptWindow.document.write(`
            <html>
                <head>
                    <title>Podcast Script</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        pre { white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <h1>AI Podcast Script</h1>
                    <pre>${script}</pre>
                </body>
            </html>
        `);
        scriptWindow.document.close();
    }

    updateUI() {
        this.updateCurrentPageUI();
        this.updateSavedItemsUI();
        this.updateFilterOptions();
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
