// Enhanced popup script for Chrome extension
class PopupManager {
    constructor() {
        this.currentPageData = null;
        this.savedItems = [];
        this.filteredItems = [];
        this.currentFilters = {};
        this.searchQuery = '';
        this.categories = { categories: [], tags: [], types: [] };
        this.init();
    }

    async init() {
        await this.loadCategories();
        await this.loadSavedItems();
        await this.loadCurrentPageData();
        this.setupEventListeners();
        this.updateUI();
    }

    async loadCurrentPageData() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab || !tab.url || tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                this.showError('Cannot save this type of page');
                return;
            }

            // Always inject content script to ensure it's fresh
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    files: ['content.js']
                });

                // Wait a bit for the script to initialize
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (e) {
                console.log('Content script injection failed:', e);
            }

            // Try to get page data from content script with retries
            let response = null;
            let attempts = 0;
            const maxAttempts = 3;

            while (attempts < maxAttempts && (!response || !response.success)) {
                try {
                    response = await chrome.tabs.sendMessage(tab.id, { action: 'getPageData' });
                    if (response && response.success) {
                        break;
                    }
                } catch (e) {
                    console.log(`Attempt ${attempts + 1} failed:`, e);
                }

                attempts++;
                if (attempts < maxAttempts) {
                    // Wait before retry, increasing delay each time
                    await new Promise(resolve => setTimeout(resolve, 200 * attempts));
                }
            }

            if (response && response.success) {
                this.currentPageData = response.data;
                this.updateCurrentPageUI();
            } else {
                // Fallback to basic tab data
                console.log('Using fallback data for tab:', tab.url);
                this.currentPageData = {
                    url: tab.url,
                    title: tab.title || 'Untitled Page',
                    type: {
                        type: 'webpage',
                        subtype: 'general',
                        icon: '🌐',
                        label: 'Web Page'
                    },
                    description: '',
                    author: '',
                    publishDate: '',
                    thumbnail: '',
                    metadata: {},
                    timestamp: Date.now()
                };
                this.updateCurrentPageUI();
            }
        } catch (error) {
            console.error('Error loading page data:', error);
            this.showError('Failed to load page information');
        }
    }

    async loadCategories() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getCategories' });
            if (response.success) {
                this.categories = response.data;
                this.updateFilterOptions();
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async loadSavedItems(filters = {}) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getSavedPages',
                filters: filters
            });

            if (response.success) {
                this.savedItems = response.data;
                this.filteredItems = [...this.savedItems];
                this.updateSavedItemsUI();
            }
        } catch (error) {
            console.error('Error loading saved items:', error);
            this.showError('Failed to load saved items');
        }
    }

    async searchSavedItems(query, filters = {}) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'searchPages',
                query: query,
                filters: filters
            });

            if (response.success) {
                this.filteredItems = response.data;
                this.updateSavedItemsUI();
            }
        } catch (error) {
            console.error('Error searching items:', error);
            this.showError('Failed to search items');
        }
    }

    setupEventListeners() {
        // Save button
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.saveCurrentPage();
        });

        // Clear all button
        document.getElementById('clearBtn').addEventListener('click', () => {
            this.clearAllItems();
        });

        // Stats button
        document.getElementById('statsBtn').addEventListener('click', () => {
            this.showStats();
        });

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');

        searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.debounceSearch();
        });

        searchBtn.addEventListener('click', () => {
            this.performSearch();
        });

        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // Filter functionality
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.currentFilters.category = e.target.value;
            this.applyFilters();
        });

        document.getElementById('typeFilter').addEventListener('change', (e) => {
            this.currentFilters.type = e.target.value;
            this.applyFilters();
        });

        document.getElementById('sortFilter').addEventListener('change', (e) => {
            const [sortBy, sortOrder] = e.target.value.split('-');
            this.currentFilters.sortBy = sortBy;
            this.currentFilters.sortOrder = sortOrder || 'desc';
            this.applyFilters();
        });

        // Modal functionality
        document.getElementById('closePreview').addEventListener('click', () => {
            this.hidePreview();
        });

        document.getElementById('closeStats').addEventListener('click', () => {
            this.hideStats();
        });

        // Close modals on background click
        document.getElementById('previewModal').addEventListener('click', (e) => {
            if (e.target.id === 'previewModal') {
                this.hidePreview();
            }
        });

        document.getElementById('statsModal').addEventListener('click', (e) => {
            if (e.target.id === 'statsModal') {
                this.hideStats();
            }
        });
    }

    debounceSearch() {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch();
        }, 300);
    }

    async performSearch() {
        if (this.searchQuery.trim()) {
            await this.searchSavedItems(this.searchQuery, this.currentFilters);
        } else {
            await this.loadSavedItems(this.currentFilters);
        }
    }

    async applyFilters() {
        if (this.searchQuery.trim()) {
            await this.searchSavedItems(this.searchQuery, this.currentFilters);
        } else {
            await this.loadSavedItems(this.currentFilters);
        }
    }

    updateCurrentPageUI() {
        if (!this.currentPageData) return;

        const typeIcon = document.getElementById('typeIcon');
        const typeText = document.getElementById('typeText');
        const pageTitle = document.getElementById('pageTitle');
        const pageUrl = document.getElementById('pageUrl');
        const saveBtn = document.getElementById('saveBtn');

        typeIcon.textContent = this.currentPageData.type.icon;
        typeText.textContent = this.currentPageData.type.label;
        pageTitle.textContent = this.currentPageData.title;
        pageUrl.textContent = this.currentPageData.url;

        // Enable save button
        saveBtn.disabled = false;
    }

    updateFilterOptions() {
        const categoryFilter = document.getElementById('categoryFilter');
        const typeFilter = document.getElementById('typeFilter');

        // Update category filter
        categoryFilter.innerHTML = '<option value="">All Categories</option>';
        this.categories.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });

        // Update type filter
        typeFilter.innerHTML = '<option value="">All Types</option>';
        this.categories.types.forEach(type => {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = type.charAt(0).toUpperCase() + type.slice(1);
            typeFilter.appendChild(option);
        });
    }

    updateSavedItemsUI() {
        const savedList = document.getElementById('savedList');
        const emptyState = document.getElementById('emptyState');
        const savedCount = document.getElementById('savedCount');

        // Update count
        const totalCount = this.savedItems.length;
        const filteredCount = this.filteredItems.length;

        if (filteredCount !== totalCount) {
            savedCount.textContent = `${filteredCount} of ${totalCount} items`;
        } else {
            savedCount.textContent = `${totalCount} item${totalCount !== 1 ? 's' : ''}`;
        }

        if (this.filteredItems.length === 0) {
            emptyState.style.display = 'block';
            savedList.innerHTML = '';
            savedList.appendChild(emptyState);

            if (this.searchQuery || Object.keys(this.currentFilters).length > 0) {
                emptyState.querySelector('.empty-text').textContent = 'No matching pages found';
                emptyState.querySelector('.empty-subtext').textContent = 'Try adjusting your search or filters';
            } else {
                emptyState.querySelector('.empty-text').textContent = 'No saved pages yet';
                emptyState.querySelector('.empty-subtext').textContent = 'Click "Save Page" to get started';
            }
            return;
        }

        emptyState.style.display = 'none';
        savedList.innerHTML = '';

        this.filteredItems.forEach((item, index) => {
            const itemElement = this.createSavedItemElement(item, index);
            savedList.appendChild(itemElement);
        });
    }

    createSavedItemElement(item, index) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'saved-item';

        const date = new Date(item.timestamp).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // Enhanced metadata display
        const category = item.category?.primary || 'General';
        const readingTime = item.readingTime ? `${item.readingTime} min read` : '';
        const wordCount = item.wordCount ? `${item.wordCount} words` : '';

        // Processing status
        let processingStatus = '';
        if (item.processingStatus === 'queued' || item.processingStatus === 'processing') {
            processingStatus = '<div class="processing-indicator">⏳ Processing...</div>';
        } else if (item.processingStatus === 'complete') {
            processingStatus = '<div class="processing-indicator complete">✅ Enhanced</div>';
        } else if (item.processingStatus === 'error') {
            processingStatus = '<div class="processing-indicator error">❌ Processing failed</div>';
        }

        // Tags display
        const tagsHtml = item.tags && item.tags.length > 0
            ? `<div class="item-tags">${item.tags.slice(0, 3).map(tag => `<span class="item-tag">${tag}</span>`).join('')}</div>`
            : '';

        itemDiv.innerHTML = `
            <div class="item-header">
                <div class="item-type">
                    <span>${item.type.icon}</span>
                    <span>${item.type.label}</span>
                </div>
                <div class="item-actions">
                    ${item.content?.text ? '<button class="item-preview-btn" title="Preview content">👁️</button>' : ''}
                    <button class="delete-btn" data-index="${index}" title="Delete">🗑️</button>
                </div>
            </div>
            <div class="item-title">${item.title}</div>
            <div class="item-url">${item.url}</div>
            <div class="item-meta">
                <span class="item-category">${category}</span>
                ${readingTime ? `<span class="item-reading-time">📖 ${readingTime}</span>` : ''}
                ${wordCount ? `<span class="item-word-count">📝 ${wordCount}</span>` : ''}
            </div>
            ${tagsHtml}
            <div class="item-date">${date}</div>
            ${processingStatus}
        `;

        // Add click handler to open URL
        itemDiv.addEventListener('click', (e) => {
            if (!e.target.classList.contains('delete-btn') && !e.target.classList.contains('item-preview-btn')) {
                chrome.tabs.create({ url: item.url });
            }
        });

        // Add delete handler
        const deleteBtn = itemDiv.querySelector('.delete-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteItem(this.savedItems.findIndex(savedItem => savedItem.id === item.id || savedItem.url === item.url));
            });
        }

        // Add preview handler
        const previewBtn = itemDiv.querySelector('.item-preview-btn');
        if (previewBtn) {
            previewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.showPreview(item);
            });
        }

        return itemDiv;
    }

    async saveCurrentPage() {
        if (!this.currentPageData) {
            this.showError('No page data available');
            return;
        }

        try {
            // Check if already saved
            const existingIndex = this.savedItems.findIndex(item => item.url === this.currentPageData.url);
            
            if (existingIndex !== -1) {
                // Update existing item
                this.savedItems[existingIndex] = { ...this.currentPageData, timestamp: Date.now() };
                this.showSuccess('Page updated successfully');
            } else {
                // Add new item
                this.savedItems.unshift(this.currentPageData);
                this.showSuccess('Page saved successfully');
            }

            // Save to storage
            await chrome.storage.local.set({ savedPages: this.savedItems });
            this.updateSavedItemsUI();

        } catch (error) {
            console.error('Error saving page:', error);
            this.showError('Failed to save page');
        }
    }

    async deleteItem(index) {
        try {
            this.savedItems.splice(index, 1);
            await chrome.storage.local.set({ savedPages: this.savedItems });
            this.updateSavedItemsUI();
            this.showSuccess('Item deleted');
        } catch (error) {
            console.error('Error deleting item:', error);
            this.showError('Failed to delete item');
        }
    }

    async clearAllItems() {
        if (this.savedItems.length === 0) return;

        if (confirm('Are you sure you want to delete all saved pages?')) {
            try {
                this.savedItems = [];
                await chrome.storage.local.set({ savedPages: [] });
                this.updateSavedItemsUI();
                this.showSuccess('All items cleared');
            } catch (error) {
                console.error('Error clearing items:', error);
                this.showError('Failed to clear items');
            }
        }
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showMessage(message, type = '') {
        const statusMessage = document.getElementById('statusMessage');
        statusMessage.textContent = message;
        statusMessage.className = `status-message show ${type}`;
        
        setTimeout(() => {
            statusMessage.classList.remove('show');
        }, 3000);
    }

    showPreview(item) {
        const modal = document.getElementById('previewModal');
        const title = document.getElementById('previewTitle');
        const meta = document.getElementById('previewMeta');
        const text = document.getElementById('previewText');

        title.textContent = item.title;

        // Build metadata display
        const metaInfo = [];
        if (item.author) metaInfo.push(`Author: ${item.author}`);
        if (item.category?.primary) metaInfo.push(`Category: ${item.category.primary}`);
        if (item.readingTime) metaInfo.push(`Reading time: ${item.readingTime} minutes`);
        if (item.wordCount) metaInfo.push(`Word count: ${item.wordCount}`);
        if (item.publishDate) {
            const pubDate = new Date(item.publishDate).toLocaleDateString();
            metaInfo.push(`Published: ${pubDate}`);
        }

        meta.innerHTML = metaInfo.join(' • ');

        // Display content
        if (item.content?.text) {
            text.textContent = item.content.text.substring(0, 2000) + (item.content.text.length > 2000 ? '...' : '');
        } else if (item.content?.excerpt) {
            text.textContent = item.content.excerpt;
        } else {
            text.textContent = item.description || 'No content preview available.';
        }

        modal.style.display = 'flex';
    }

    hidePreview() {
        document.getElementById('previewModal').style.display = 'none';
    }

    async showStats() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getStats' });
            if (response.success) {
                const stats = response.data;
                const modal = document.getElementById('statsModal');
                const body = document.getElementById('statsBody');

                body.innerHTML = `
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value">${stats.totalSaved || 0}</span>
                            <span class="stat-label">Total Saved</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.totalReadingTime || 0}</span>
                            <span class="stat-label">Minutes to Read</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.totalWordCount || 0}</span>
                            <span class="stat-label">Total Words</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${stats.averageReadingTime || 0}</span>
                            <span class="stat-label">Avg. Reading Time</span>
                        </div>
                    </div>

                    ${stats.categoryBreakdown ? this.createBreakdownHTML('Categories', stats.categoryBreakdown) : ''}
                    ${stats.typeBreakdown ? this.createBreakdownHTML('Content Types', stats.typeBreakdown) : ''}
                `;

                modal.style.display = 'flex';
            }
        } catch (error) {
            console.error('Error loading stats:', error);
            this.showError('Failed to load statistics');
        }
    }

    createBreakdownHTML(title, breakdown) {
        const items = Object.entries(breakdown)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([key, value]) => `
                <div class="breakdown-item">
                    <span class="breakdown-label">${key}</span>
                    <span class="breakdown-count">${value}</span>
                </div>
            `).join('');

        return `
            <div class="${title.toLowerCase().replace(' ', '-')}-breakdown">
                <div class="breakdown-title">${title}</div>
                ${items}
            </div>
        `;
    }

    hideStats() {
        document.getElementById('statsModal').style.display = 'none';
    }

    updateUI() {
        this.updateCurrentPageUI();
        this.updateSavedItemsUI();
        this.updateFilterOptions();
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
