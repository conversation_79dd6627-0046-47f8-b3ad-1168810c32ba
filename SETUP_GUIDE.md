# 🎙️ AI Podcast Generation Setup Guide

## Quick Start (Demo Mode)

**The easiest way to test the podcast generation:**

1. **Save some content** - Add 3-5 articles to your Page Saver extension
2. **Click the 🎙️ button** in the extension popup
3. **Leave API key fields empty** for demo mode
4. **Choose your settings** (hosts, length, focus area)
5. **Click "Generate Podcast"** - you'll get a demo podcast with your content!

## Demo Mode Features

✅ **Works immediately** - No API keys required  
✅ **Uses your saved content** - Generates script based on your articles  
✅ **Browser text-to-speech** - Basic audio generation  
✅ **Full UI experience** - See how the system works  

❌ **Limited audio quality** - Uses browser's built-in voices  
❌ **Shorter content** - Demo scripts are simplified  
❌ **No GPT-4 generation** - Uses template-based scripts  

## Full Mode Setup (Professional Quality)

For NotebookLM-quality podcasts with GPT-4 and professional voices:

### Option 1: Browser Extension Only (Limitations)

**Current Status:** ⚠️ **Not fully functional due to browser security restrictions**

The browser extension alone cannot make direct API calls to OpenAI and ElevenLabs due to CORS (Cross-Origin Resource Sharing) policies. This is a security feature that prevents websites from making unauthorized requests to external APIs.

### Option 2: With Proxy Server (Recommended)

**Status:** ✅ **Fully functional with setup**

1. **Install Node.js** (if not already installed)
   ```bash
   # Download from https://nodejs.org/
   node --version  # Should show version number
   ```

2. **Set up the proxy server**
   ```bash
   # Create a new directory
   mkdir podcast-server
   cd podcast-server
   
   # Initialize npm project
   npm init -y
   
   # Install dependencies
   npm install express cors node-fetch
   ```

3. **Copy the server file**
   - Copy `podcast-proxy-server.js` to your `podcast-server` directory

4. **Get API Keys**
   - **OpenAI**: https://platform.openai.com/api-keys (requires paid account)
   - **ElevenLabs**: https://elevenlabs.io/app/speech-synthesis (requires paid plan)

5. **Set environment variables** (optional but recommended)
   ```bash
   export OPENAI_API_KEY="your-openai-key-here"
   export ELEVENLABS_API_KEY="your-elevenlabs-key-here"
   ```

6. **Start the server**
   ```bash
   node podcast-proxy-server.js
   ```
   You should see: "Podcast generation server running on port 3001"

7. **Use the extension**
   - Open the extension
   - Click 🎙️ to generate podcast
   - Enter your API keys (or use environment variables)
   - Generate professional-quality podcasts!

### Option 3: Cloud Deployment (Advanced)

Deploy the proxy server to a cloud service:

**Heroku:**
```bash
# Install Heroku CLI
# Create new app
heroku create your-podcast-app

# Set environment variables
heroku config:set OPENAI_API_KEY=your-key
heroku config:set ELEVENLABS_API_KEY=your-key

# Deploy
git push heroku main
```

**Vercel/Netlify:** Similar process with serverless functions

## Troubleshooting

### "No audio generated"
- **Demo Mode**: Make sure you have saved content and try again
- **Full Mode**: Check that the proxy server is running on port 3001
- **API Keys**: Verify your API keys are valid and have sufficient credits

### "Server not available"
- Make sure the proxy server is running: `node podcast-proxy-server.js`
- Check the URL: http://localhost:3001/health should return "OK"
- Try restarting the server

### "API key errors"
- **OpenAI**: Ensure you have a paid account with GPT-4 access
- **ElevenLabs**: Ensure you have a paid plan (free tier is very limited)
- **Format**: API keys should be strings without quotes when entered in the UI

### "Poor audio quality in demo mode"
- This is expected - demo mode uses browser text-to-speech
- For professional quality, use full mode with ElevenLabs

## Cost Estimates

### OpenAI (GPT-4)
- **10-minute podcast**: ~$0.10-0.50 depending on content complexity
- **Rate limits**: 3 requests per minute on paid plans

### ElevenLabs
- **10-minute podcast**: ~$0.30-1.00 depending on your plan
- **Character limits**: Varies by subscription tier

### Total Cost
- **Demo podcast (10 min)**: Free
- **Professional podcast (10 min)**: ~$0.40-1.50

## Alternative Solutions

If the full setup is too complex, consider:

1. **Use demo mode** for testing and content organization
2. **Export scripts** and use other TTS services
3. **Manual recording** using the generated scripts as a guide
4. **Wait for updates** - we're working on simpler solutions

## Future Improvements

We're working on:
- **Browser-compatible API calls** using different approaches
- **Simplified setup** with one-click deployment
- **Alternative TTS providers** that work directly in browsers
- **Offline generation** using local models

## Support

If you encounter issues:
1. Check this guide first
2. Verify your setup matches the requirements
3. Test with demo mode to isolate issues
4. Check browser console for error messages

The demo mode should work immediately and give you a good sense of the system's capabilities!
