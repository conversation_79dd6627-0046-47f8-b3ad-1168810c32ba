// Test script for the podcast generation API
// Run with: node test-api.js

const API_BASE_URL = 'https://your-project-name.vercel.app'; // Update this URL

const testData = {
  savedItems: [
    {
      id: "1",
      title: "The Future of AI in Software Development",
      url: "https://example.com/ai-development",
      description: "Exploring how artificial intelligence is transforming the way we write, test, and deploy software applications.",
      content: {
        text: "Artificial intelligence is revolutionizing software development in unprecedented ways. From automated code generation to intelligent debugging, AI tools are becoming indispensable for modern developers. This transformation is not just about efficiency; it's about fundamentally changing how we approach problem-solving in software engineering.",
        excerpt: "AI is revolutionizing software development through automated code generation and intelligent debugging tools."
      },
      category: {
        primary: "Technology"
      },
      type: {
        type: "article",
        label: "Article"
      },
      author: "<PERSON>",
      siteName: "TechInsights",
      tags: ["AI", "software development", "automation", "programming"],
      readingTime: 8,
      wordCount: 1200
    },
    {
      id: "2", 
      title: "Machine Learning Best Practices for Production",
      url: "https://example.com/ml-production",
      description: "A comprehensive guide to deploying machine learning models in production environments with reliability and scalability.",
      content: {
        text: "Deploying machine learning models to production requires careful consideration of numerous factors including model versioning, monitoring, data drift detection, and performance optimization. This guide covers the essential practices that ensure your ML systems remain robust and reliable in real-world scenarios.",
        excerpt: "Essential practices for deploying ML models including versioning, monitoring, and drift detection."
      },
      category: {
        primary: "Technology"
      },
      type: {
        type: "article", 
        label: "Article"
      },
      author: "Dr. Alex Chen",
      siteName: "ML Engineering",
      tags: ["machine learning", "production", "deployment", "MLOps"],
      readingTime: 12,
      wordCount: 1800
    },
    {
      id: "3",
      title: "The Ethics of AI: Balancing Innovation and Responsibility",
      url: "https://example.com/ai-ethics",
      description: "Examining the ethical implications of AI development and the need for responsible innovation in the tech industry.",
      content: {
        text: "As AI systems become more powerful and pervasive, the ethical considerations surrounding their development and deployment have become increasingly critical. This article explores the balance between pushing the boundaries of innovation and ensuring responsible, fair, and transparent AI systems.",
        excerpt: "Exploring the balance between AI innovation and responsible development practices."
      },
      category: {
        primary: "Technology"
      },
      type: {
        type: "article",
        label: "Article"
      },
      author: "Prof. Sarah Johnson",
      siteName: "AI Ethics Review",
      tags: ["AI ethics", "responsible AI", "innovation", "technology policy"],
      readingTime: 10,
      wordCount: 1500
    }
  ],
  hostA: "alex",
  hostB: "sam", 
  targetLength: 8,
  focusArea: "technology",
  style: "conversational"
};

async function testPodcastGeneration() {
  console.log('🎙️ Testing Podcast Generation API...');
  console.log(`📡 API URL: ${API_BASE_URL}/api/generate-podcast`);
  console.log(`📊 Test data: ${testData.savedItems.length} articles, ${testData.targetLength} minutes`);
  
  try {
    console.log('\n⏳ Sending request...');
    const startTime = Date.now();
    
    const response = await fetch(`${API_BASE_URL}/api/generate-podcast`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'chrome-extension://test-extension-id' // Simulate extension origin
      },
      body: JSON.stringify(testData)
    });
    
    const duration = Date.now() - startTime;
    console.log(`⏱️  Request completed in ${duration}ms`);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    console.log('✅ Response received successfully!');
    console.log(`📦 Content-Type: ${response.headers.get('content-type')}`);
    console.log(`📏 Content-Length: ${response.headers.get('content-length')} bytes`);
    
    // Get the audio blob
    const audioBlob = await response.blob();
    console.log(`🎵 Audio blob size: ${audioBlob.size} bytes`);
    console.log(`🎵 Audio type: ${audioBlob.type}`);
    
    // Save to file for testing
    if (typeof require !== 'undefined') {
      const fs = require('fs');
      const buffer = Buffer.from(await audioBlob.arrayBuffer());
      const filename = `test-podcast-${Date.now()}.mp3`;
      fs.writeFileSync(filename, buffer);
      console.log(`💾 Audio saved as: ${filename}`);
    }
    
    console.log('\n🎉 Test completed successfully!');
    console.log('🔊 You can now play the generated audio file.');
    
  } catch (error) {
    console.error('\n❌ Test failed:');
    console.error(error.message);
    
    if (error.message.includes('fetch')) {
      console.log('\n💡 Troubleshooting tips:');
      console.log('1. Make sure the API URL is correct');
      console.log('2. Verify the Vercel deployment is successful');
      console.log('3. Check that environment variables are set');
      console.log('4. Ensure the API is not rate-limited');
    }
  }
}

// Health check function
async function healthCheck() {
  console.log('🏥 Performing health check...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/generate-podcast`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'chrome-extension://test-extension-id'
      }
    });
    
    if (response.ok) {
      console.log('✅ API is healthy and responding to CORS preflight');
      return true;
    } else {
      console.log(`⚠️  API responded with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Health check failed: ${error.message}`);
    return false;
  }
}

// Run the test
async function runTest() {
  console.log('🚀 Starting API Test Suite\n');
  
  // First do a health check
  const isHealthy = await healthCheck();
  
  if (!isHealthy) {
    console.log('\n⚠️  Health check failed. Skipping main test.');
    console.log('Please check your deployment and try again.');
    return;
  }
  
  console.log('\n📝 Running main podcast generation test...');
  await testPodcastGeneration();
}

// Check if running in Node.js environment
if (typeof require !== 'undefined' && require.main === module) {
  // Running in Node.js
  const fetch = require('node-fetch');
  runTest();
} else {
  // Running in browser - export for manual testing
  window.testPodcastAPI = {
    testPodcastGeneration,
    healthCheck,
    runTest
  };
  console.log('Test functions available as window.testPodcastAPI');
}
