# Page Saver Chrome Extension

A modern Chrome extension built with Manifest V3 that allows you to save and organize web pages with intelligent content detection.

## Features

### 🚀 Core Functionality
- **One-click page saving** - Save current webpage with a single click
- **Intelligent content detection** - Automatically identifies articles, YouTube videos, PDFs, and more
- **Local storage** - All data stored locally using Chrome Storage API
- **Clean, modern UI** - Professional interface with responsive design
- **Content type icons** - Visual indicators for different content types

### 📱 User Interface
- **Popup interface** - Clean, scrollable list of saved items
- **Real-time updates** - Instant feedback for user actions
- **Status messages** - Success/error notifications
- **Loading states** - Visual feedback during operations
- **Responsive design** - Works perfectly in the popup window

### 🔍 Content Detection
- **Article detection** - News articles and blog posts
- **YouTube videos** - Video metadata extraction
- **PDF documents** - Document type identification
- **E-commerce pages** - Product page detection
- **Social media** - Twitter/X and LinkedIn posts
- **General web pages** - Fallback for any webpage

### 💾 Data Management
- **Persistent storage** - Data survives browser restarts
- **Duplicate handling** - Updates existing saves instead of duplicating
- **Bulk operations** - Clear all saved items option
- **Individual deletion** - Remove specific saved items
- **Automatic cleanup** - Optional cleanup of old items

## Installation

### For Development/Testing:

1. **Download the extension files** to a local directory
2. **Add icons** (see `icons/README.md` for details)
3. **Open Chrome** and navigate to `chrome://extensions/`
4. **Enable Developer mode** (toggle in top-right corner)
5. **Click "Load unpacked"** and select the extension directory
6. **Pin the extension** to your toolbar for easy access

### File Structure:
```
page-saver-extension/
├── manifest.json          # Extension configuration
├── popup.html             # Popup interface
├── popup.js               # Popup functionality
├── content.js             # Page analysis script
├── background.js          # Service worker
├── styles.css             # UI styling
├── icons/                 # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   ├── icon128.png
│   └── README.md
└── README.md              # This file
```

## Usage

### Saving Pages:
1. **Navigate** to any webpage you want to save
2. **Click** the Page Saver extension icon in your toolbar
3. **Review** the detected page information
4. **Click "Save Page"** to add it to your collection

### Managing Saved Pages:
- **View saved items** in the popup's scrollable list
- **Click any saved item** to open it in a new tab
- **Delete individual items** using the trash icon
- **Clear all items** using the clear button (with confirmation)

### Content Types Detected:
- 📰 **Articles** - News articles and blog posts
- 🎥 **YouTube Videos** - Video content with metadata
- 📄 **PDF Documents** - Document files
- 🛒 **Product Pages** - E-commerce listings
- 🐦 **Social Media** - Twitter/X and LinkedIn posts
- 🌐 **Web Pages** - General websites

## Technical Details

### Manifest V3 Features:
- **Service Worker** - Modern background script architecture
- **Content Scripts** - Intelligent page analysis
- **Storage API** - Reliable local data persistence
- **Scripting API** - Dynamic content script injection
- **Permissions** - Minimal required permissions

### Permissions Used:
- `storage` - Save and retrieve user data
- `activeTab` - Access current tab information
- `scripting` - Inject content analysis scripts
- `host_permissions` - Access webpage content

### Browser Compatibility:
- Chrome 88+ (Manifest V3 support)
- Chromium-based browsers (Edge, Brave, etc.)

## Development

### Code Structure:
- **popup.js** - Main popup interface logic
- **content.js** - Page analysis and metadata extraction
- **background.js** - Service worker for data management
- **styles.css** - Modern, responsive UI styling

### Key Classes:
- `PopupManager` - Handles popup UI and user interactions
- `PageAnalyzer` - Analyzes webpage content and extracts metadata
- `BackgroundService` - Manages data storage and extension lifecycle

### Storage Schema:
```javascript
{
  savedPages: [
    {
      url: string,
      title: string,
      type: { type, subtype, icon, label },
      description: string,
      author: string,
      publishDate: string,
      thumbnail: string,
      metadata: object,
      timestamp: number
    }
  ],
  settings: {
    maxSavedItems: number,
    autoCleanup: boolean,
    cleanupDays: number,
    showNotifications: boolean
  },
  stats: {
    totalSaved: number,
    lastUpdate: number
  }
}
```

## Customization

### Adding New Content Types:
1. Modify the `detectContentType()` method in `content.js`
2. Add new detection logic and return appropriate type object
3. Update CSS in `styles.css` for any new styling needs

### Changing UI Appearance:
- Modify `styles.css` for visual changes
- Update `popup.html` for structural changes
- Adjust colors, fonts, and layout as needed

### Extending Functionality:
- Add new methods to `PopupManager` class
- Extend `PageAnalyzer` for better content detection
- Modify `BackgroundService` for additional data management

## Troubleshooting

### Common Issues:

**Extension not loading:**
- Ensure all files are in the correct directory
- Check for syntax errors in JavaScript files
- Verify manifest.json is valid JSON

**Content not detected:**
- Some websites may block content scripts
- Try refreshing the page after installing
- Check browser console for error messages

**Data not saving:**
- Verify storage permissions are granted
- Check if storage quota is exceeded
- Clear extension data and try again

**Icons not showing:**
- Add actual PNG icon files to the icons/ directory
- Ensure icon files are named correctly
- Check file permissions

### Debug Mode:
1. Open Chrome DevTools (F12)
2. Go to Extensions tab
3. Find Page Saver and click "Inspect views: popup"
4. Check console for error messages

## License

This extension is provided as-is for educational and personal use. Feel free to modify and distribute according to your needs.

## Contributing

To contribute to this extension:
1. Fork the repository
2. Make your changes
3. Test thoroughly
4. Submit a pull request with detailed description

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review browser console for error messages
3. Ensure you're using a compatible Chrome version
4. Try disabling other extensions that might conflict
