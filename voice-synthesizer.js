// ElevenLabs Voice Synthesis Integration
class VoiceSynthesizer {
    constructor() {
        this.apiKey = null;
        this.baseUrl = 'https://api.elevenlabs.io/v1';
        
        this.voiceProfiles = {
            alex: {
                voiceId: 'pNInz6obpgDQGcFmaJgB', // Adam - thoughtful male voice
                settings: {
                    stability: 0.75,
                    similarity_boost: 0.85,
                    style: 0.65,
                    use_speaker_boost: true
                },
                actorMode: {
                    speaking_rate: 0.95,
                    emotional_range: 0.7,
                    emphasis_strength: 0.6,
                    pause_length: 1.2
                },
                characteristics: {
                    age: "30s",
                    accent: "neutral_american",
                    tone: "thoughtful_analytical",
                    energy: "medium"
                }
            },
            sam: {
                voiceId: 'EXAVITQu4vr4xnSDxMaL', // Bella - enthusiastic female voice
                settings: {
                    stability: 0.65,
                    similarity_boost: 0.80,
                    style: 0.85,
                    use_speaker_boost: true
                },
                actorMode: {
                    speaking_rate: 1.05,
                    emotional_range: 0.9,
                    emphasis_strength: 0.8,
                    pause_length: 0.9
                },
                characteristics: {
                    age: "20s",
                    accent: "neutral_american",
                    tone: "enthusiastic_engaging",
                    energy: "high"
                }
            },
            jordan: {
                voiceId: 'ErXwobaYiN019PkySvjV', // Antoni - practical male voice
                settings: {
                    stability: 0.80,
                    similarity_boost: 0.75,
                    style: 0.55,
                    use_speaker_boost: true
                },
                actorMode: {
                    speaking_rate: 1.0,
                    emotional_range: 0.6,
                    emphasis_strength: 0.7,
                    pause_length: 1.0
                },
                characteristics: {
                    age: "40s",
                    accent: "neutral_american",
                    tone: "practical_grounded",
                    energy: "medium"
                }
            },
            riley: {
                voiceId: 'MF3mGyEYCl7XYWbV9V6O', // Elli - creative female voice
                settings: {
                    stability: 0.70,
                    similarity_boost: 0.82,
                    style: 0.75,
                    use_speaker_boost: true
                },
                actorMode: {
                    speaking_rate: 0.98,
                    emotional_range: 0.85,
                    emphasis_strength: 0.75,
                    pause_length: 1.1
                },
                characteristics: {
                    age: "30s",
                    accent: "neutral_american",
                    tone: "creative_expressive",
                    energy: "medium_high"
                }
            }
        };

        this.audioSettings = {
            output_format: 'mp3_44100_128',
            optimize_streaming_latency: 3,
            model_id: 'eleven_multilingual_v2'
        };
    }

    setApiKey(apiKey) {
        this.apiKey = apiKey;
    }

    async synthesizeScript(script, hostA, hostB, options = {}) {
        if (!this.apiKey) {
            throw new Error('ElevenLabs API key not configured');
        }

        const {
            includeBackgroundMusic = true,
            musicVolume = 0.3,
            exportQuality = '320kbps',
            normalizeAudio = true
        } = options;

        try {
            // Parse script into segments
            const segments = this.parseScriptSegments(script, hostA, hostB);
            
            // Generate audio for each segment
            const audioSegments = await this.generateAudioSegments(segments);
            
            // Mix and master the final audio
            const finalAudio = await this.mixAndMasterAudio(
                audioSegments,
                includeBackgroundMusic,
                musicVolume,
                normalizeAudio
            );

            return {
                audioBlob: finalAudio,
                metadata: {
                    duration: await this.getAudioDuration(finalAudio),
                    segments: segments.length,
                    hosts: [hostA, hostB],
                    quality: exportQuality,
                    generatedAt: new Date().toISOString()
                }
            };
        } catch (error) {
            console.error('Error synthesizing script:', error);
            throw new Error(`Voice synthesis failed: ${error.message}`);
        }
    }

    parseScriptSegments(script, hostA, hostB) {
        const lines = script.split('\n').filter(line => line.trim());
        const segments = [];
        
        for (const line of lines) {
            // Skip music cues and stage directions
            if (line.includes('[MUSIC]') || line.includes('[END]')) {
                continue;
            }

            // Parse host dialogue
            const hostAPattern = new RegExp(`\\[${hostA.toUpperCase()}\\]:\\s*(.+)`, 'i');
            const hostBPattern = new RegExp(`\\[${hostB.toUpperCase()}\\]:\\s*(.+)`, 'i');
            
            let match = line.match(hostAPattern);
            if (match) {
                segments.push({
                    host: hostA,
                    text: this.processDialogueText(match[1]),
                    voiceProfile: this.voiceProfiles[hostA],
                    timing: this.extractTimingCues(match[1])
                });
                continue;
            }

            match = line.match(hostBPattern);
            if (match) {
                segments.push({
                    host: hostB,
                    text: this.processDialogueText(match[1]),
                    voiceProfile: this.voiceProfiles[hostB],
                    timing: this.extractTimingCues(match[1])
                });
            }
        }

        return segments;
    }

    processDialogueText(text) {
        // Remove stage directions and convert to SSML-like markup
        let processedText = text
            .replace(/\[PAUSE\]/g, '<break time="0.8s"/>')
            .replace(/\[THINKING\]/g, '<break time="0.5s"/>')
            .replace(/\[EMPHASIS\]/g, '')
            .replace(/\[OVERLAPPING\]/g, '')
            .replace(/\[INTERRUPTING\]/g, '')
            .replace(/\[EXCITED\]/g, '')
            .replace(/\[THOUGHTFUL\]/g, '')
            .replace(/\[AGREEMENT\]/g, '')
            .replace(/\[BUILDING ON IDEA\]/g, '')
            .replace(/\[REFLECTIVE\]/g, '')
            .replace(/\[FORWARD-LOOKING\]/g, '');

        // Add natural pauses after punctuation
        processedText = processedText
            .replace(/\.\s+/g, '. <break time="0.4s"/>')
            .replace(/\?\s+/g, '? <break time="0.5s"/>')
            .replace(/!\s+/g, '! <break time="0.4s"/>')
            .replace(/,\s+/g, ', <break time="0.2s"/>');

        return processedText.trim();
    }

    extractTimingCues(text) {
        const cues = {
            hasEmphasis: text.includes('[EMPHASIS]'),
            isPause: text.includes('[PAUSE]'),
            isThinking: text.includes('[THINKING]'),
            isExcited: text.includes('[EXCITED]'),
            isInterrupting: text.includes('[INTERRUPTING]'),
            isOverlapping: text.includes('[OVERLAPPING]')
        };

        return cues;
    }

    async generateAudioSegments(segments) {
        const audioSegments = [];
        
        for (const segment of segments) {
            try {
                const audioBlob = await this.synthesizeSegment(segment);
                audioSegments.push({
                    ...segment,
                    audio: audioBlob,
                    duration: await this.getAudioDuration(audioBlob)
                });
            } catch (error) {
                console.error(`Error synthesizing segment for ${segment.host}:`, error);
                // Add silence as fallback
                audioSegments.push({
                    ...segment,
                    audio: await this.generateSilence(2000), // 2 seconds of silence
                    duration: 2000
                });
            }
        }

        return audioSegments;
    }

    async synthesizeSegment(segment) {
        const { voiceProfile, text, timing } = segment;
        
        // Adjust voice settings based on timing cues
        const adjustedSettings = this.adjustVoiceSettings(voiceProfile.settings, timing);
        
        const requestBody = {
            text: text,
            model_id: this.audioSettings.model_id,
            voice_settings: adjustedSettings,
            output_format: this.audioSettings.output_format
        };

        const response = await fetch(`${this.baseUrl}/text-to-speech/${voiceProfile.voiceId}`, {
            method: 'POST',
            headers: {
                'Accept': 'audio/mpeg',
                'Content-Type': 'application/json',
                'xi-api-key': this.apiKey
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
        }

        return await response.blob();
    }

    adjustVoiceSettings(baseSettings, timing) {
        const adjusted = { ...baseSettings };
        
        if (timing.isExcited) {
            adjusted.stability = Math.max(0.4, adjusted.stability - 0.1);
            adjusted.style = Math.min(1.0, adjusted.style + 0.1);
        }
        
        if (timing.isThinking) {
            adjusted.stability = Math.min(0.9, adjusted.stability + 0.1);
            adjusted.style = Math.max(0.3, adjusted.style - 0.1);
        }
        
        if (timing.hasEmphasis) {
            adjusted.similarity_boost = Math.min(1.0, adjusted.similarity_boost + 0.05);
        }

        return adjusted;
    }

    async mixAndMasterAudio(audioSegments, includeBackgroundMusic, musicVolume, normalizeAudio) {
        // This would integrate with Web Audio API for mixing
        // For now, return a simple concatenation
        
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const segments = [];
        
        for (const segment of audioSegments) {
            const arrayBuffer = await segment.audio.arrayBuffer();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            segments.push(audioBuffer);
        }

        // Calculate total duration
        const totalDuration = segments.reduce((sum, buffer) => sum + buffer.duration, 0);
        
        // Create output buffer
        const outputBuffer = audioContext.createBuffer(
            2, // stereo
            Math.ceil(totalDuration * audioContext.sampleRate),
            audioContext.sampleRate
        );

        // Mix segments
        let currentTime = 0;
        for (const buffer of segments) {
            const startSample = Math.floor(currentTime * audioContext.sampleRate);
            
            for (let channel = 0; channel < Math.min(2, buffer.numberOfChannels); channel++) {
                const outputData = outputBuffer.getChannelData(channel);
                const inputData = buffer.getChannelData(Math.min(channel, buffer.numberOfChannels - 1));
                
                for (let i = 0; i < inputData.length && startSample + i < outputData.length; i++) {
                    outputData[startSample + i] = inputData[i];
                }
            }
            
            currentTime += buffer.duration;
        }

        // Convert to blob
        const wav = this.audioBufferToWav(outputBuffer);
        return new Blob([wav], { type: 'audio/wav' });
    }

    audioBufferToWav(buffer) {
        const length = buffer.length;
        const numberOfChannels = buffer.numberOfChannels;
        const sampleRate = buffer.sampleRate;
        const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
        const view = new DataView(arrayBuffer);
        
        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * numberOfChannels * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * numberOfChannels * 2, true);
        view.setUint16(32, numberOfChannels * 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, length * numberOfChannels * 2, true);
        
        // Convert float samples to 16-bit PCM
        let offset = 44;
        for (let i = 0; i < length; i++) {
            for (let channel = 0; channel < numberOfChannels; channel++) {
                const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
        }
        
        return arrayBuffer;
    }

    async generateSilence(durationMs) {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const sampleRate = audioContext.sampleRate;
        const samples = Math.ceil(durationMs * sampleRate / 1000);
        
        const buffer = audioContext.createBuffer(2, samples, sampleRate);
        // Buffer is already filled with zeros (silence)
        
        const wav = this.audioBufferToWav(buffer);
        return new Blob([wav], { type: 'audio/wav' });
    }

    async getAudioDuration(audioBlob) {
        return new Promise((resolve) => {
            const audio = new Audio();
            audio.onloadedmetadata = () => {
                resolve(audio.duration * 1000); // Return in milliseconds
            };
            audio.src = URL.createObjectURL(audioBlob);
        });
    }

    async getAvailableVoices() {
        if (!this.apiKey) {
            throw new Error('ElevenLabs API key not configured');
        }

        const response = await fetch(`${this.baseUrl}/voices`, {
            headers: {
                'xi-api-key': this.apiKey
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch voices: ${response.status}`);
        }

        const data = await response.json();
        return data.voices;
    }

    async cloneVoice(audioFile, voiceName, description) {
        if (!this.apiKey) {
            throw new Error('ElevenLabs API key not configured');
        }

        const formData = new FormData();
        formData.append('name', voiceName);
        formData.append('description', description);
        formData.append('files', audioFile);

        const response = await fetch(`${this.baseUrl}/voices/add`, {
            method: 'POST',
            headers: {
                'xi-api-key': this.apiKey
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error(`Voice cloning failed: ${response.status}`);
        }

        return await response.json();
    }
}

// Make VoiceSynthesizer available globally
window.VoiceSynthesizer = VoiceSynthesizer;
