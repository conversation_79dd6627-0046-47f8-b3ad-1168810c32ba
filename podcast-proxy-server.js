// Simple Node.js proxy server for podcast generation
// This handles API calls that can't be made directly from the browser extension

const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));

// Store API keys (in production, use environment variables)
let OPENAI_API_KEY = process.env.OPENAI_API_KEY;
let ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

// Endpoint to set API keys
app.post('/api/set-keys', (req, res) => {
    const { openaiKey, elevenlabsKey } = req.body;
    OPENAI_API_KEY = openaiKey;
    ELEVENLABS_API_KEY = elevenlabsKey;
    res.json({ success: true, message: 'API keys set successfully' });
});

// Endpoint to generate script using OpenAI
app.post('/api/generate-script', async (req, res) => {
    try {
        if (!OPENAI_API_KEY) {
            return res.status(400).json({ error: 'OpenAI API key not configured' });
        }

        const { prompt } = req.body;

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${OPENAI_API_KEY}`
            },
            body: JSON.stringify({
                model: 'gpt-4-turbo-preview',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert podcast script writer specializing in creating natural, engaging conversations between AI hosts. Your scripts should sound authentic, insightful, and genuinely conversational, matching the quality of NotebookLM.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 4000,
                temperature: 0.8,
                top_p: 0.9
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        res.json({ script: data.choices[0].message.content });

    } catch (error) {
        console.error('Error generating script:', error);
        res.status(500).json({ error: error.message });
    }
});

// Endpoint to synthesize voice using ElevenLabs
app.post('/api/synthesize-voice', async (req, res) => {
    try {
        if (!ELEVENLABS_API_KEY) {
            return res.status(400).json({ error: 'ElevenLabs API key not configured' });
        }

        const { text, voiceId, voiceSettings } = req.body;

        const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
            method: 'POST',
            headers: {
                'Accept': 'audio/mpeg',
                'Content-Type': 'application/json',
                'xi-api-key': ELEVENLABS_API_KEY
            },
            body: JSON.stringify({
                text: text,
                model_id: 'eleven_multilingual_v2',
                voice_settings: voiceSettings
            })
        });

        if (!response.ok) {
            throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
        }

        const audioBuffer = await response.buffer();
        
        // Convert to base64 for transmission
        const audioBase64 = audioBuffer.toString('base64');
        
        res.json({ 
            audio: audioBase64,
            contentType: 'audio/mpeg'
        });

    } catch (error) {
        console.error('Error synthesizing voice:', error);
        res.status(500).json({ error: error.message });
    }
});

// Endpoint to generate complete podcast
app.post('/api/generate-podcast', async (req, res) => {
    try {
        const { savedItems, options } = req.body;
        
        // Step 1: Generate script
        const scriptPrompt = buildPodcastPrompt(savedItems, options);
        
        const scriptResponse = await fetch('http://localhost:3001/api/generate-script', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ prompt: scriptPrompt })
        });
        
        const { script } = await scriptResponse.json();
        
        // Step 2: Parse script and generate audio segments
        const segments = parseScriptSegments(script, options.hostA, options.hostB);
        const audioSegments = [];
        
        for (const segment of segments) {
            const voiceResponse = await fetch('http://localhost:3001/api/synthesize-voice', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    text: segment.text,
                    voiceId: segment.voiceId,
                    voiceSettings: segment.voiceSettings
                })
            });
            
            const { audio } = await voiceResponse.json();
            audioSegments.push(audio);
        }
        
        // Step 3: Combine audio segments (simplified)
        // In a real implementation, you'd use audio processing libraries
        const combinedAudio = audioSegments[0]; // Simplified - just return first segment
        
        res.json({
            success: true,
            script: script,
            audio: combinedAudio,
            metadata: {
                segments: segments.length,
                duration: 'estimated',
                hosts: [options.hostA, options.hostB]
            }
        });
        
    } catch (error) {
        console.error('Error generating podcast:', error);
        res.status(500).json({ error: error.message });
    }
});

function buildPodcastPrompt(savedItems, options) {
    const { hostA, hostB, targetLength, focusArea, style } = options;
    
    return `Create a natural, engaging podcast conversation between two AI hosts discussing saved web content.

HOST A: ${hostA} - analytical and thoughtful
HOST B: ${hostB} - enthusiastic and energetic

CONTENT TO DISCUSS:
${savedItems.map(item => `- "${item.title}": ${item.description}`).join('\n')}

TARGET LENGTH: ${targetLength} minutes
STYLE: ${style}
FOCUS: ${focusArea}

Generate a natural conversation script with realistic dialogue, interruptions, and genuine reactions. Format as:
[${hostA.toUpperCase()}]: dialogue
[${hostB.toUpperCase()}]: dialogue`;
}

function parseScriptSegments(script, hostA, hostB) {
    const lines = script.split('\n').filter(line => line.trim());
    const segments = [];
    
    const voiceProfiles = {
        alex: { voiceId: 'pNInz6obpgDQGcFmaJgB', settings: { stability: 0.75, similarity_boost: 0.85 } },
        sam: { voiceId: 'EXAVITQu4vr4xnSDxMaL', settings: { stability: 0.65, similarity_boost: 0.80 } },
        jordan: { voiceId: 'ErXwobaYiN019PkySvjV', settings: { stability: 0.80, similarity_boost: 0.75 } },
        riley: { voiceId: 'MF3mGyEYCl7XYWbV9V6O', settings: { stability: 0.70, similarity_boost: 0.82 } }
    };
    
    for (const line of lines) {
        const hostAPattern = new RegExp(`\\[${hostA.toUpperCase()}\\]:\\s*(.+)`, 'i');
        const hostBPattern = new RegExp(`\\[${hostB.toUpperCase()}\\]:\\s*(.+)`, 'i');
        
        let match = line.match(hostAPattern);
        if (match) {
            segments.push({
                host: hostA,
                text: match[1].replace(/\[.*?\]/g, '').trim(),
                voiceId: voiceProfiles[hostA].voiceId,
                voiceSettings: voiceProfiles[hostA].settings
            });
            continue;
        }
        
        match = line.match(hostBPattern);
        if (match) {
            segments.push({
                host: hostB,
                text: match[1].replace(/\[.*?\]/g, '').trim(),
                voiceId: voiceProfiles[hostB].voiceId,
                voiceSettings: voiceProfiles[hostB].settings
            });
        }
    }
    
    return segments;
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'Podcast generation server is running' });
});

app.listen(PORT, () => {
    console.log(`Podcast generation server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
