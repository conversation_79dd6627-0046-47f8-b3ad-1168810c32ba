# Extension Icons

This directory should contain the following icon files for the Chrome extension:

## Required Icon Files:
- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (Windows computers often require this size)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store and installation)

## Icon Design Guidelines:
- Use a simple, recognizable design
- Ensure good contrast and visibility at small sizes
- Consider using a book, bookmark, or save icon theme
- Use PNG format with transparency
- Follow Google's Material Design principles

## Temporary Solution:
For testing purposes, you can:
1. Create simple colored squares as placeholder icons
2. Use any image editing software (GIMP, Photoshop, Canva, etc.)
3. Or download free icons from sites like:
   - Google Material Icons
   - Feather Icons
   - Heroicons
   - Flaticon (with attribution)

## Quick Icon Creation:
You can create simple placeholder icons using online tools:
- favicon.io
- canva.com
- figma.com

The extension will work without icons, but Chrome will show default placeholder icons instead.
